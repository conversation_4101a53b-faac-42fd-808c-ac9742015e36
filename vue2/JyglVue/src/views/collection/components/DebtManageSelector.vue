<template>
  <el-dialog
    title="选择隐性债券"
    :visible.sync="dialogVisible"
    width="80%"
    :close-on-click-modal="false"
    append-to-body
    custom-class="accounts-select-dialog"
    @close="handleClose"
  >
    <div>
      <div>
        <el-form :model="queryParams" label-position="left" size="small" label-width="85px">
          <el-row :gutter="24">
            <el-col :span="6">
              <el-form-item label="公司名称：">
                <el-select v-model="queryParams.companyName"  filterable clearable  placeholder="请选择公司名称" @change="handleChange">
                  <el-option
                      v-for="item in companyOptions"
                      :key="item"
                      :label="item"
                      :value="item">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="项目编码：">
                <el-input
                  size="mini"
                  v-model="queryParams.projectCode"
                  clearable
                  placeholder="请输入项目编码"
                  @change="handleChange"
                  @keyup.enter.native="getTableList()"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="项目名称：">
                <el-input
                  size="mini"
                  v-model="queryParams.projectName"
                  clearable
                  placeholder="请输入项目名称"
                  @change="handleChange"
                  @keyup.enter.native="getTableList()"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="12" style="margin-bottom: 10px;">
            <el-col :span="12">
              <el-button
                type="text"
                icon="el-icon-search"
                size="small"
                @click="getTableList()"
              >
                查询
              </el-button>
              <el-button
                type="text"
                icon="el-icon-refresh"
                size="small"
                @click="handleReset()"
              >
                重置
              </el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <!-- 应收账款选择表格 -->
      <div>
        <el-table
          :data="accountsReceivableList"
          border
          style="width: 100%"
          max-height="410"
          @row-click="handleAccountRowClick"
          highlight-current-row
          v-loading="loading"
        >
          <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
          <el-table-column prop="companyName" label="单位名称" min-width="150"></el-table-column>
          <el-table-column prop="projectCode" label="项目编号" width="150"></el-table-column>
          <el-table-column prop="projectName" label="项目名称" min-width="240"></el-table-column>
          <el-table-column prop="customerName" label="客户名称" width="100"></el-table-column>
          <el-table-column prop="projectCompletedDate" label="项目完工时间" width="100">
            <!-- 项目完工时间 -->
            <template slot-scope="scope">
              <!-- 只保留年月日 -->
              {{ scope.row.projectCompletedDate ? scope.row.projectCompletedDate.split(' ')[0] : '' }}
            </template>
          </el-table-column>
          <el-table-column prop="contractAmount" label="合同金额（元）" width="120" align="right">
            <template slot-scope="scope">
              <span class="amount-text">{{ formatAmount(scope.row.contractAmount) }}</span>
            </template>
          </el-table-column>

        </el-table>
        <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
        />
      </div>
    </div>

    <span slot="footer" style="text-align: end;">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleConfirmSelect" :disabled="!selectedAccount">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  getList,
  getCompanyOptions
} from "api/jygl/debtManagement";

import Pagination from 'components/Pagination/index.vue';

export default {
  name: 'DebtManagementSelector',
  components: {
    Pagination
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  tableType:{
    type:Number,
    default:1
  },
  data() {
    return {
      loading: false,
      selectedAccount: null, // 选中的应收账款
      accountsReceivableList: [],
      companyOptions:[],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        companyName: '',
        unit: '',
        projectCode: '',
        projectName: '',
        isSummary:false
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.getTableList();
        this.getCompanyOptions()
      }
    }
  },
  methods: {
    //获取单位选项
    getCompanyOptions() {
        getCompanyOptions().then((res) => {
          this.companyOptions = ['全部',...res.result]
        })
    },
    // 获取表格数据
    getTableList() {
      this.loading = true;
      getList(this.queryParams).then((res) => {
        this.accountsReceivableList = res.result.records;
        this.queryParams.total = res.result.total;
      }).catch(() => {
        this.$message.error('获取数据失败');
      }).finally(() => {
        this.loading = false;
      });
    },

    // 处理搜索条件变化
    handleChange() {
      this.queryParams.pageNum = 1
      this.getTableList()

    },

    // 重置搜索条件
    handleReset() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        companyName: '',
        unit: '',
        projectCode: '',
        projectName: ''
      };
      this.getTableList();
    },

    // 分页查询
    handleCurrentChange(val) {
      this.queryParams = val;
      this.getTableList();
    },

    // 处理账款行点击
    handleAccountRowClick(row) {
      this.selectedAccount = row;
    },

    // 确认选择账款
    handleConfirmSelect() {
      if (this.selectedAccount) {
        this.$emit('confirm', this.selectedAccount);
        this.handleCancel();
      } else {
        this.$message.warning('请先选择一条应收账款记录');
      }
    },

    // 取消选择
    handleCancel() {
      this.dialogVisible = false;
      this.selectedAccount = null;
    },

    // 对话框关闭事件
    handleClose() {
      this.selectedAccount = null;
    },

    // 格式化金额显示
    formatAmount(amount) {
      if (!amount) return '0.00';
      return Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    }
  }
};
</script>

<style scoped>
.amount-text {
  color: #f56c6c;
  font-weight: 500;
}

/* 应收账款选择对话框样式 */
::v-deep .accounts-select-dialog {
  .el-dialog__body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
  }
}

/* 表格行悬停效果 */
::v-deep .el-table tbody tr:hover > td {
  background-color: #f5f7fa !important;
}

/* 表格当前行高亮 */
::v-deep .el-table tbody tr.current-row > td {
  background-color: #ecf5ff !important;
}
</style>
