<template>
  <div class="main" ref="main">
    <div class="process-info" style="width: 100%;height: 100%">
      <div class="search-container">
        <div class="operate-pannel">
          <div class="search-box">
<!--            <span class="font-size14">单位名称：</span>-->
<!--            <el-input v-model="queryParams.unit" clearable placeholder="请输入单位名称"-->
<!--                      @change="handleChange" style="width: 300px;"></el-input>-->
            <span class="font-size14">公司名称：</span>
            <el-select v-model="queryParams.companyName" style="width: 300px" filterable clearable  placeholder="请选择单位名称" @change="handleChange">
              <el-option
                  v-for="item in companyOptions"
                  :key="item"
                  :label="item"
                  :value="item">
              </el-option>
            </el-select>
            <span class="font-size14">项目编码：</span>
            <el-input v-model="queryParams.projectCode" clearable placeholder="请输入项目编码"
                      @change="handleChange" style="width: 300px;"></el-input>
            <span class="font-size14">项目名称：</span>
            <el-input v-model="queryParams.projectName" clearable placeholder="请输入项目名称"
                      @change="handleChange"></el-input>
            <el-button
                size="mini"
                type="text"
                icon="el-icon-search"
                @click="query()"
                v-if="this.$store.getters.permissions.indexOf('JDWJY01YXZJ02QX01') > -1"
            >
              查询
            </el-button>
<!--            <el-button-->
<!--                type="text"-->
<!--                size="mini"-->
<!--                icon="el-icon-upload2"-->
<!--                v-if="this.$store.getters.permissions.indexOf('JDWJY01YSZK01QX02') > -1"-->
<!--                @click="openFileUpload"-->
<!--                :loading="importLoading"-->
<!--            >-->
<!--              导入Excel-->
<!--            </el-button>-->
            <el-button
                type="text"
                size="mini"
                icon="el-icon-upload2"
                v-if="this.$store.getters.permissions.indexOf('JDWJY01YSZK01QX02') > -1"
                @click="()=>{importDialogVisible = true}"
            >
              导入Excel
            </el-button>
            <el-button
                size="mini"
                type="text"
                icon="el-icon-download"
                @click="exportExcel()"
            >
              导出Excel
            </el-button>
            <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
          </div>
        </div>
        <div class="table-box">
          <Table
              :tableData="accountsReceivableList"
              :tableOptions="realTableOptions"
              :loading="debtManagementLoading"
              @getCurrentData="select"
          >
            <template slot-scope="scope">
              {{
                (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
              }}
            </template>

          </Table>
          <Pagination
              @handleRefresh="handleCurrentChange"
              :queryParam="queryParams"
              layout="total, sizes, prev, pager, next, jumper"
              :total="queryParams.total"
          />

          <!-- 隐藏的文件上传组件 -->
          <el-upload
              ref="fileupload"
              style="display: none"
              action=""
              :before-upload="beforeUpload"
              :http-request="uploadHttpRequest"
              :show-file-list="false"
              accept=".xlsx,.xls">
            <el-button class="uploac-button">上传文件</el-button>
          </el-upload>
        </div>
      </div>

      <el-dialog title="应收账款数据导入" :visible.sync="importDialogVisible" width="410px" :close-on-click-modal="false" append-to-body center custom-class="import-dialog">
        <div class="import-dialog-content">
          <FileImport
              uploadUrl="/accountsReceivable/importExcel"
              downloadName="应收账款数据导入模板"
              :limit="1"
              :fileSize="100"
              :apiFunctions="apiFunctions"
              @uploadSuccessData="handleUploadSuccess"
              @uploadErrorData="handleUploadError"
          />
        </div>
      </el-dialog>


    </div>
  </div>
</template>
<script>
import Table from 'components/MainTable/index.vue'
import Pagination from 'components/Pagination/index.vue'
import Dropdown from 'components/ColumnDropdown/index.vue'
import FileImport from 'components/UploadFile/FileImport.vue'

import {
  getList,
  importExcel,
  downLoadExcel,
  exportExcelTemplate,
  getAccountsReceivableCompanyOptions
} from "api/jygl/accountsReceivable";
import {downLoad} from "@/utils/tool";

export default {
  name: 'index',
  components: {Table, Pagination, Dropdown,FileImport},
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        companyName: '',
        unit: '',
        projectCode: '',
        projectName: '',
        isSummary: 0
      },
      importDialogVisible: false,
      apiFunctions:{
        exportExcelTemplate:exportExcelTemplate,
        importExcel: importExcel

      },
      realTableOptions: [],
      tableOptions: [
        {label: '公司名称', prop: 'companyName'},
        {label: '单位', prop: 'unit'},
        {label: '科目编号', prop: 'subjectNumber'},
        {label: '科目名称', prop: 'subjectName'},
        {label: '客户档案编号' , prop: 'customerFileNumber'},
        {label: '客户档案' , prop: 'customer'},
        {label: '项目类型编号' , prop: 'projectTypeCode'},
        {label: '项目类型名称' , prop: 'projectTypeName'},
        {label: '项目编号' , prop: 'projectCode'},
        {label: '项目名称' , prop: 'projectName'},

        {label: '凭证号' , prop: 'transferVoucher'},
        {label: '金额' , prop: 'money'},
        {label: '款项发生时间' , prop: 'transferTime'},
        {label: '账龄（天数）' , prop: 'agingOfAccounts'},
        {label: '账龄时间分段' , prop: 'agingBuckets'},
        {label: '单位来源' , prop: 'fundingSource'},
        {label: '客商分类' , prop: 'businessPartnerClassification'},
        {label: '添加年月' , prop: 'addTime'},
        {label: '清除年月' , prop: 'removeTime'},
        {label: '摘要' , prop: 'remark'},

      ],
      debtManagementLoading: false,
      importLoading: false,
      accountsReceivableList: [],
      selectID: 0,
      companyOptions:[]
    };
  },
  methods: {
    getCompanyOptions(){
      getAccountsReceivableCompanyOptions().then((res) => {
        this.companyOptions = ['全部',...res.result]
      })
    },

    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },

    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.query()
    },

    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val
      this.query()
    },

    /** 单击表事件 */
    select(row) {
      this.selectID = row.Id
    },

    // 查询方法
    query(){
      console.log("this.$route.query======",this.$route.query)
      this.debtManagementLoading = true
      getList(this.queryParams).then((res) => {
        this.accountsReceivableList = res.result.records
        this.queryParams.total = res.result.total

        // 如果路由存在参数，清空query参数并设置isSummary为0
        if (this.$route.query && Object.keys(this.$route.query).length > 0) {
          // 清空路由参数
          this.$router.replace({ query: {} });
          // 设置isSummary为0  用于下一次查询
          this.queryParams.isSummary = 0;
        }
      }).finally(() => {
        this.debtManagementLoading = false
      })
    },

    // 打开文件上传
    openFileUpload(){
      this.$refs.fileupload.$el.querySelector('.uploac-button').click();
    },
    // 导出excel
    exportExcel() {
      // 显示导出遮罩层
      const loading = this.$loading({
        lock: true,
        text: '正在导出中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      downLoadExcel(this.queryParams).then((res) => {
        let fileName ='应收账款数据'  + new Date().getTime() + '.xlsx'
        downLoad(res, fileName)
      }).catch((error) => {
        console.error('导出失败:', error);
        this.$message.error('导出失败，请重试');
      }).finally(() => {
        // 关闭遮罩层
        loading.close();
      });
    },

    //导入excel
    beforeUpload(file){
      // 获取上传excel文件的信息
      const fileContent = file.raw;
      // 获取文件类型
      const types = file.name.split(".")[1];
      const fileType = ["xlsx", "xls"].some(
          (item) => item === types
      );
      if (!fileContent) {
        if (!fileType) {
          alert("格式错误！请重新选择");
          return;
        }
      }
    },

    // 导入excel处理
    uploadHttpRequest(item) {
      this.importLoading = true
      const form = new FormData()
      form.append('file', item.file)
      importExcel(form).then(res => {
        if (res.code==200){
          this.$message.success(res.message)
          this.query();
        }else {
          this.$message.error(res.message)
        }
        this.$refs.fileupload.clearFiles();
      }).catch(() => {
        this.$refs.fileupload.clearFiles();
      }).finally(() => {
        this.importLoading = false
      })
    },

    // 处理文件上传成功
    handleUploadSuccess(message) {
      this.$message.success(message);
      this.importDialogVisible = false; // 关闭对话框
      this.query();
    },

    // 处理文件上传失败
    handleUploadError(message) {
      this.$message.error(message || '导入失败');
      this.importDialogVisible = false; // 关闭对话框
    },


  },

  created() {
    // 初始化表格列配置
    this.realTableOptions = [...this.tableOptions]

    // 检查是否有从汇总页面传递过来的查询参数
    if (this.$route.query.companyName) {
      this.queryParams.companyName = this.$route.query.companyName
      this.queryParams.isSummary = this.$route.query.isSummary
    }

    this.query()
    this.getCompanyOptions()
  }
};
</script>
<style lang="scss" scoped>

.main {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
}


.process-info {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  transition: all .5s;
}

.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 100px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}
.abow_dialog {
  display: flex;
  justify-content: center;
  align-items: Center;
  overflow: hidden;
  .el-dialog {
    margin: 0 auto !important;
    height: 90%;
    overflow: hidden;
    .el-dialog__body {
      position: absolute;
      left: 0;
      top: 54px;
      bottom: 0;
      right: 0;
      padding: 0;
      z-index: 1;
      overflow: hidden;
      overflow-y: auto;
    }
  }
}

</style>
