import request from '@/utils/request'

/** 隐性债券列表 */
export function getList (params) {
    return request({
        url: '/dwJygl/collection/getList',
        method: 'post',
        data: params
    })
}

export function getInfo (params) {
    return request({
        url: '/dwJygl/collection/getInfo',
        method: 'post',
        data: params
    })
}

export function sendEmail(params){
    return request({
        url: '/dwJygl/collection/sendEmail',
        method: 'post',
        data: params
    })
}


export function confirmStatus(params){
    return request({
        url: '/dwJygl/collection/confirm',
        method: 'post',
        data: params
    })
}


export function addCollection(params){
    return request({
        url: '/dwJygl/collection/add',
        method: 'post',
        data: params
    })
}

export function updateCollection(param){
    return request({
        url: '/dwJygl/collection/update',
        method: 'post',
        data: param
    })
}






/**
 * 导入excel
 */
export function importExcel(params){
    return request({
        url: '/dwJygl/accountsReceivable/importExcel',
        method: 'post',
        data: params,
        headers: { "Content-Type": "multipart/form-data" }
    })
}

/** 导出excel模板 */
export function exportExcelTemplate () {
    return request({
        url: '/dwJygl/accountsReceivable/exportExcelTemplate',
        method: 'post',
        responseType: 'blob'
    })
}



