import request from '@/utils/request'

/**
 * 获取主表列表（一级列表）
 * @param params
 * @returns {*}
 */
export function getMainList (params) {
    return request({
        url: '/dwSjks/sjks/ewm/getMainList',
        method: 'post',
        data: params
    })
}

/**
 * 获取详情列表（二级列表）
 * @param params
 * @returns {*}
 */
export function getDetailList (params) {
    return request({
        url: '/dwSjks/sjks/ewm/getDetailList',
        method: 'post',
        data: params
    })
}

/**
 * 二维码列表（已废弃，建议使用getDetailList）
 * @param params
 * @returns {*}
 */
export function getList (params) {
    return request({
        url: '/dwSjks/sjks/ewm/getList',
        method: 'post',
        data: params
    })
}

/**
 * 生成二维码并导出PDF
 * @param params
 * @returns {*}
 */
export function generateQrcode (params) {
    return request({
        url: '/dwSjks/sjks/ewm/generate',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}

/**
 * 根据主表ID导出PDF
 * @param qrCodeId
 * @returns {*}
 */
export function exportPdfByQrCodeId (qrCodeId) {
    return request({
        url: `/dwSjks/sjks/ewm/exportPdf/${qrCodeId}`,
        method: 'post',
        responseType: 'blob'
    })
}