<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.0.0 http://maven.apache.org/xsd/assembly-2.0.0.xsd">
    <id>package</id>
    <formats>
        <format>zip</format>
    </formats>
    <includeBaseDirectory>true</includeBaseDirectory>
    <fileSets>
        <!--拷贝application.yml文件到jar包的外部config目录下面-->
        <fileSet>
            <directory>${basedir}/src/main/resources</directory>
            <includes>
                <include>*.yml</include>
            </includes>
            <filtered>true</filtered>
            <outputDirectory>${file.separator}config</outputDirectory>
        </fileSet>

        <fileSet>
            <!-- 需要打包的路径 -->
            <directory>${basedir}/runscript/shell</directory>
            <!-- 打包后输出的路径，自定义 -->
            <outputDirectory>${file.separator}</outputDirectory>
        </fileSet>

        <!--拷贝本地资源包到jar包的外部lib下面-->
        <fileSet>
            <directory>${basedir}/jar</directory>
            <outputDirectory>${file.separator}lib</outputDirectory>
            <!-- 打包需要包含的文件 -->
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>

        <!--拷贝static目录下所有文档到static目录-->
        <fileSet>
            <directory>${basedir}/src/main/resources/static</directory>
            <outputDirectory>${file.separator}static</outputDirectory>
        </fileSet>

        <!--拷贝lib包到jar包的外部lib下面-->
        <fileSet>
            <directory>${project.build.directory}/lib</directory>
            <outputDirectory>${file.separator}lib</outputDirectory>
            <!-- 打包需要包含的文件 -->
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>

        <fileSet>
            <directory>${project.build.directory}</directory>
            <outputDirectory>${file.separator}</outputDirectory>
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>
    </fileSets>
</assembly>
