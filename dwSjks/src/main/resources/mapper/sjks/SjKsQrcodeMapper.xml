<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.sjks.mapper.SjKsQrcodeMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.sjks.domain.SjKsQrcode">
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="year" column="year" jdbcType="INTEGER"/>
            <result property="month" column="month" jdbcType="INTEGER"/>
            <result property="batch" column="batch" jdbcType="INTEGER"/>
            <result property="qr_code_number" column="qr_code_number" jdbcType="INTEGER"/>
            <result property="create_by" column="create_by" jdbcType="VARCHAR"/>
            <result property="create_time" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="update_by" column="update_by" jdbcType="VARCHAR"/>
            <result property="update_time" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,year,month,
        batch,qr_code_number,create_by,
        create_time,update_by,update_time,
        remark
    </sql>
</mapper>
