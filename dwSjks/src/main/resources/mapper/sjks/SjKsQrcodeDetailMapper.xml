<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.sjks.mapper.SjKsQrcodeDetailMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.sjks.domain.SjKsQrcodeDetail">
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="qr_code_id" column="qr_code_id" jdbcType="INTEGER"/>
            <result property="qr_code" column="qr_code" jdbcType="VARCHAR"/>
            <result property="user_id" column="user_id" jdbcType="INTEGER"/>
            <result property="binding_time" column="binding_time" jdbcType="TIMESTAMP"/>
            <result property="input_score" column="input_score" jdbcType="DECIMAL"/>
            <result property="input_user_id" column="input_user_id" jdbcType="INTEGER"/>
            <result property="input_time" column="input_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,qr_code_id,qr_code,
        user_id,binding_time,input_score,
        input_user_id,input_time
    </sql>

    <!-- 批量插入二维码详情记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO sj_ks_qrcode_detail (
            qr_code_id,
            qr_code,
            create_by,
            create_time,
            deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.qrCodeId},
                #{item.qrCode},
                #{item.createBy},
                #{item.createTime},
                #{item.deleted}
            )
        </foreach>
    </insert>

</mapper>
