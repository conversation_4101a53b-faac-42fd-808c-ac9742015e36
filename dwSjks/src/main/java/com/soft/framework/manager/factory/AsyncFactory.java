package com.soft.framework.manager.factory;

import com.soft.framework.common.constant.Constants;
import com.soft.framework.common.utils.ServletUtils;
import com.soft.framework.common.utils.date.DateUtil;
import com.soft.framework.common.utils.ip.IpUtils;
import com.soft.framework.helper.DBHelper;
import com.soft.framework.helper.LogHelper;
import com.soft.gcc.base.entity.sysloginfo;
import com.soft.gcc.base.entity.sysoperlog;
import eu.bitwalker.useragentutils.UserAgent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Date;
import java.util.TimerTask;

/**
 * 异步工厂（产生任务用）
 * 
 * <AUTHOR>
 */
public class AsyncFactory
{
    private static final Logger sys_user_logger = LoggerFactory.getLogger("sys-user");

    /**
     * 记录登陆信息
     * 
     * @param loginname 用户名
     * @param status 状态
     * @param message 消息
     * @param args 列表
     * @return 任务task
     */
    public static TimerTask recordLogininfor(final String loginname, final String status, final String message, final Object... args)
    {
        final UserAgent userAgent = UserAgent.parseUserAgentString(ServletUtils.getRequest().getHeader("User-Agent"));
        final String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
        return new TimerTask()
        {
            @Override
            public void run()
            {
//                String address = AddressUtils.getRealAddressByIP(ip);
                StringBuilder s = new StringBuilder();
                s.append(LogHelper.getBlock(ip));
//                s.append(address);
                s.append(LogHelper.getBlock(loginname));
                s.append(LogHelper.getBlock(status));
                s.append(LogHelper.getBlock(message));
                // 打印信息到日志
                sys_user_logger.info(s.toString(), args);
                // 获取客户端操作系统
                String os = userAgent.getOperatingSystem().getName();
                // 获取客户端浏览器
                String browser = userAgent.getBrowser().getName();
                // 封装对象
                sysloginfo logininfor = new sysloginfo();
                logininfor.setUserName(loginname);
                logininfor.setIpAddr(ip);
//                logininfor.setLoginLocation(address);
                logininfor.setBrowser(browser);
                logininfor.setOs(os);
                logininfor.setMsg(message);
                logininfor.setLoginTime(DateUtil.toLocalDateTime(new Date()));
                // 日志状态
                if (Constants.LOGIN_SUCCESS.equals(status) || Constants.LOGOUT.equals(status))
                {
                    logininfor.setStatus(Constants.SUCCESS);
                }
                else if (Constants.LOGIN_FAIL.equals(status))
                {
                    logininfor.setStatus(Constants.FAIL);
                }

                String strsql = DBHelper.GetInsertSQL(logininfor,"sysloginfo", Arrays.asList("LogId"));
                DBHelper.ExecuteSql(strsql);
            }
        };
    }

    /**
     * 操作日志记录
     * 
     * @param operLog 操作日志信息
     * @return 任务task
     */
    public static TimerTask recordOper(final sysoperlog operLog)
    {
        return new TimerTask()
        {
            @Override
            public void run()
            {
            // 远程查询操作地点
            String strsql = DBHelper.GetInsertSQL(operLog,"sysoperlog", Arrays.asList("LogId"));
            DBHelper.ExecuteSql(strsql);
            }
        };
    }
}
