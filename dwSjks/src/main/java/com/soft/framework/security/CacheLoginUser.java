package com.soft.framework.security;

import com.soft.framework.common.constant.SysVar;
import org.apache.logging.log4j.util.Strings;

import java.io.Serializable;
public class CacheLoginUser implements Serializable {

    public static final long serialVersionUID = 1L;
    /**
     * 登录人账号
     */
    String loginName;

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public long getlLoginTime() {
        return lLoginTime;
    }

    public void setlLoginTime(long lLoginTime) {
        this.lLoginTime = lLoginTime;
    }

    public int getErrorTime() {
        return errorTime;
    }

    public void setErrorTime(int errorTime) {
        this.errorTime = errorTime;
    }

    /**
     * 上次登录时间
     */
    long lLoginTime;
    /**
     * 登录失败次数
     */
    int errorTime;

    public CacheLoginUser(String loginName){
        this.loginName = loginName;
        this.lLoginTime = System.currentTimeMillis();
        this.errorTime = 0;
    }

    public CacheLoginUser(){
        this.lLoginTime = System.currentTimeMillis();
        this.errorTime = 0;
    }

    public static boolean isStongPass(String loginPass) {
        if (Strings.isEmpty(loginPass)){
            return false;
        }
        return loginPass.matches(SysVar.REGEX_PASSWORD_STRONG);
    }

    public boolean isLock() {
        if (errorTime >= SysVar.MAX_ERROR_COUNT) {
            long c = (System.currentTimeMillis()-lLoginTime);
            if (c>= SysVar.MAX_ERROR_TIME*60*1000){
                return false;
            }else{
                return true;
            }
        }else{
            return false;
        }
    }

    public void error(){
        lLoginTime = System.currentTimeMillis();
        errorTime=errorTime+1;
    }

    public String lockMsg(){
        return SysVar.MAX_ERROR_TXT;
    }
    public String msg(){
        return SysVar.ERROR_TXT;
    }
}
