package com.soft.framework.controller.BaseOpt;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.DBHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.helper.WpServiceHelper;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.yyszc.extend.DataTable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@Controller
@Configuration
@RequestMapping(value = "/Service/BaseOpt/UserRole")
@Api(tags = "基本框架接口->用户角色接口")
public class UserRoleController {
    @RequestMapping(value = "/GetUserRoleList_Y", produces = {"text/plain;charset=UTF-8"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetUserRoleList_Y", notes = "获取用户具有的角色的列表的接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String GetUserRoleList_Y(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String UserId = request.getParameter("UserId");
        String RoleKind = request.getParameter("RoleKind");
        String RoleMark = request.getParameter("RoleMark");
        if (StringUtil.IsNullOrEmpty(UserId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult.toString();
        }

        try {
            strsql = "select a.* from Role a where 1=1 ";
            if (!StringUtil.IsNullOrEmpty(RoleKind) && !RoleKind.equals("0")) {
                strsql += " and a.RoleKind='" + RoleKind + "'";
            }
            if (!StringUtil.IsNullOrEmpty(RoleMark)) {
                strsql += " and a.RoleName like '" + RoleMark + "'";
            }
            strsql += " and a.id in(select RoleId from RolePerson where PersonId='" + UserId + "')";

            DataTable tmpdt = WpServiceHelper.GetDataTable(strsql);
            if (tmpdt == null) {
                ajaxResult = AjaxResult.error("操作失败，获取信息失败！");
                return ajaxResult.toString();
            }

            String jsonstr = AjaxResult.extgrid(tmpdt);
            return jsonstr;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult.toString();
        }
    }

    @RequestMapping(value = "/GetUserRoleList_W", produces = {"text/plain;charset=UTF-8"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetUserRoleList_W", notes = "获取用户不具有的角色的列表的接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String GetUserRoleList_W(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String tmpstr = "";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = StringUtil.IsNullOrEmpty(starts) ? 0 : Integer.parseInt(starts);
        int limit = StringUtil.IsNullOrEmpty(limits) ? 20 : Integer.parseInt(limits);

        String UserId = request.getParameter("UserId");
        String RoleKind = request.getParameter("RoleKind");
        String RoleMark = request.getParameter("RoleMark");
        if (StringUtil.IsNullOrEmpty(UserId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult.toString();
        }

        String basesql = "";
        String orderstr = "";
        String msgstr = "";
        int rcount = 0;

        try {
            SqlHelper sqlhelper = new SqlHelper();

            basesql = "select a.* from Role a where 1=1 ";
            if (!StringUtil.IsNullOrEmpty(RoleKind) && !RoleKind.equals("0")) {
                basesql += " and a.RoleKind='" + RoleKind + "'";
            }
            if (!StringUtil.IsNullOrEmpty(RoleMark)) {
                basesql += " and a.RoleName like '" + RoleMark + "'";
            }
            basesql += " and a.id not in(select RoleId from RolePerson where PersonId='" + UserId + "')";
            orderstr = " order by a.RoleName asc";

            String rcsql = DBHelper.ToRecordCountSql(basesql.toString());

            String rrval = "0";
            rrval = WpServiceHelper.GetRecordCount(rcsql);
            if (rrval == null || rrval.equals("")) {
                ajaxResult = AjaxResult.error("操作失败，获取数据条目信息失败！");
                return ajaxResult.toString();
            }

            if (!rrval.equals("")) {
                rcount = Integer.parseInt(rrval);
            }

            int pageCount = (rcount / limit) + 1;
            int currpage = start / limit;
            strsql = sqlhelper.ToPageSql(basesql.toString(), orderstr.toString(), limit, currpage);

            DataTable tmpdt = WpServiceHelper.GetDataTable(strsql);
            if (tmpdt == null) {
                ajaxResult = AjaxResult.error("操作失败，获取信息失败！");
                return ajaxResult.toString();
            }

            String jsonstr = AjaxResult.extgrid(tmpdt, rcount);
            return jsonstr;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult.toString();
        }
    }

    @RequestMapping(value = "/AddUserRole", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "AddUserRole", notes = "用户添加角色接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult AddUserRole(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String RoleId = request.getParameter("RoleId");
        String UserId = request.getParameter("UserId");
        if (StringUtil.IsNullOrEmpty(RoleId) || StringUtil.IsNullOrEmpty(UserId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            int iroleid = Integer.parseInt(RoleId);
            int iuserid = Integer.parseInt(UserId);

            Boolean uflag = false;
            uflag = WpServiceHelper.AddRolePerson(iroleid, iuserid);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，操作角色用户信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success("权限设置成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/DeleteUserRole", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "DeleteUserRole", notes = "用户删除角色接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult DeleteUserRole(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String RoleId = request.getParameter("RoleId");
        String UserId = request.getParameter("UserId");
        if (StringUtil.IsNullOrEmpty(RoleId) || StringUtil.IsNullOrEmpty(UserId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            int iroleid = Integer.parseInt(RoleId);
            int iuserid = Integer.parseInt(UserId);

            Boolean uflag = false;
            uflag = WpServiceHelper.DeleteRolePerson(iroleid, iuserid);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，操作角色用户信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success("权限删除成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
}
