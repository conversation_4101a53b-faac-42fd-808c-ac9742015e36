package com.soft.framework.controller.CPSoft;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.yyszc.wpbase.entity.vComp;
import com.yyszc.wpbase.entity.vPerson;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Controller
@Configuration
@RequestMapping("/Service/CpSoft/CmUserRoleEx")
public class CMUserRoleExController {

    @RequestMapping("/GetRoleUserList")
    @ResponseBody
    public AjaxResult GetRoleUserList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String CompId = request.getParameter("CompId");
        String RoleId = request.getParameter("RoleId");
        if (RoleId.equals("")) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            SqlHelper sqlhelper = new SqlHelper();

            strsql = "select * from vComp where 1=1";
            if (!StringUtil.IsNullOrEmpty(CompId)&&!CompId.equals("0")){
                strsql += " and COMP_ID=" + CompId;
            }
            strsql += " order by COMP_ID asc";

            List<vPerson> _list = new ArrayList<vPerson>();
            List<vComp> _glist = sqlhelper.GetObjectList(vComp.class, strsql);
            for (vComp tmpc:_glist)
            {
                strsql = "select * from Person where Id in (select dbo.FUNC_ROLE_USR2(" + tmpc.getCOMP_ID() + "," + RoleId + ")) ";
                List<vPerson> _plist = sqlhelper.GetObjectList(vPerson.class,strsql);
                for (vPerson pe :_plist)
                {
                    pe.setTopGroupId(tmpc.getCOMP_ID());
                    pe.setTopGroupName(tmpc.getCOMP_NAME());
                    String tmpstr=sqlhelper.ExecuteScalar("select groupname from groupitem where id='" + pe.getGroupId()+"'");
                    pe.setGroupName(tmpstr);
                }
                _list.addAll(_plist);
            }
            ajaxResult = AjaxResult.extgrid(vPerson.class, _list.size(), _list);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping("/ExtendRoleRight")
    @ResponseBody
    @RepeatSubmit
    public AjaxResult ExtendRoleRight(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String RoleId0 = request.getParameter("RoleId0");
        String RoleId1 = request.getParameter("RoleId1");
        String CompId = request.getParameter("CompId");
        if (RoleId0.equals("")||RoleId1.equals("")){
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            SqlHelper sqlhelper = new SqlHelper();

            strsql = "insert into RolePerson(RoleId,personId) select "+RoleId1+",personId from RolePerson where RoleId="+RoleId0;
            if (!StringUtil.IsNullOrEmpty(CompId)&&!CompId.equals("0")){
                String gstr = sqlhelper.ExecuteScalar("select dbo.FUNC_GRP_CLIST(" + CompId + ")");
                strsql += " and personId in(select Id from person where GroupId in(" + gstr + "))";
            }
            strsql += " and not exists(select * from RolePerson where roleid="+RoleId1+" and personId=a.PersonId)";
            sqlhelper.ExecuteNoQuery(strsql);

            ajaxResult = AjaxResult.success("含有特定权限的用户扩展指定权限成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
}
