package com.soft.framework.controller.BaseOpt;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.config.BaseConfig;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.ConfigHelper;
import com.soft.framework.helper.DBHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.helper.ToolHelper;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.yyszc.extend.DataTable;
import com.yyszc.wpbase.entity.Module;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Controller
@Configuration
@RequestMapping(value = "/Service/BaseOpt/MDLMan")
@Api(tags = "基本框架接口->模块管理接口")
public class MDLManController {

    private Boolean GatherParams2Obj(Map<String, String> params, Module entity, StringBuilder msgstr) {
        try {
            if (!StringUtil.IsNull(params.get("module_num"))) {
                entity.setModule_num(params.get("module_num"));
            }
            if (!StringUtil.IsNull(params.get("module_name"))) {
                entity.setModule_name(params.get("module_name"));
            }
            if (!StringUtil.IsNull(params.get("url"))) {
                entity.setUrl(params.get("url"));
            }
            if (!StringUtil.IsNull(params.get("app_url"))) {
                entity.setApp_url(params.get("app_url"));
            }
            if (!StringUtil.IsNull(params.get("define_f"))) {
                entity.setDefine_f(params.get("define_f"));
            }
            if (!StringUtil.IsNull(params.get("define_s"))) {
                entity.setDefine_s(params.get("define_s"));
            }

            if (!StringUtil.IsNullOrEmpty(params.get("is_show"))) {
                entity.setIs_show(Integer.parseInt(params.get("is_show")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("module_type"))) {
                entity.setModule_type(Integer.parseInt(params.get("module_type")));
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("程序产生异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/AddMDL", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "AddMDL", notes = "新增模块配置接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult AddMDL(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            SqlHelper sqlhelper = new SqlHelper();
            StringBuilder msgstr = new StringBuilder();

            Module entity = new Module();

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，参数传输失败！");
                return ajaxResult;
            }

            //提交数据库
            String strsql = DBHelper.GetInsertSQL(entity, "Module", Arrays.asList("ID"));
            if (StringUtil.IsNullOrEmpty(strsql)) {
                ajaxResult = AjaxResult.error("操作失败，生成对象对应插库语句失败！");
                return ajaxResult;
            }
            String tmpstr = sqlhelper.ExecuteInsertWithObtainId(strsql);

            ajaxResult = AjaxResult.success(tmpstr);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ModifyMDL", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ModifyMDL", notes = "修改模块配置接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult ModifyMDL(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            SqlHelper sqlhelper = new SqlHelper();
            StringBuilder msgstr = new StringBuilder();

            String Id = params.get("ID");
            String strsql = "select * from Module where ID='" + Id + "'";
            Module entity = sqlhelper.GetObject(Module.class, strsql);
            if (entity == null) {
                ajaxResult = AjaxResult.error("此记录在数据库中已经被删除，请刷新主界面，重新加载数据！");
                return ajaxResult;
            }

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，参数传输失败！");
                return ajaxResult;
            }

            //提交数据库
            strsql = DBHelper.GetUpdateSQL(entity, "Module", Arrays.asList("ID"), Arrays.asList(Id.toString()));
            if (StringUtil.IsNullOrEmpty(strsql)) {
                ajaxResult = AjaxResult.error("操作失败，生成对象对应插库语句失败！");
                return ajaxResult;
            }
            sqlhelper.ExecuteNoQuery(strsql);

            ajaxResult = AjaxResult.success(entity.getID().toString());
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/DeleteMDL", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "DeleteMDL", notes = "删除模块配置接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult DeleteMDL(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try {
            SqlHelper sqlhelper = new SqlHelper();

            String Id = request.getParameter("ID");

            String strsql = "delete from Module where ID='" + Id + "'";
            sqlhelper.ExecuteNoQuery(strsql);

            ajaxResult = AjaxResult.success("删除默认启动设置成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }


    private boolean GetUIQueryString(HttpServletRequest request, StringBuilder strsql, StringBuilder orderstr, StringBuilder msgstr) {
        try {
            String seachtj = request.getParameter("seachtj");

            strsql.append("select * from Module where 1=1 ");

            if (BaseConfig.getExtVersion().equals("3.2")) {
                String sortf = request.getParameter("sort");
                String sortd = request.getParameter("dir");
                if (!StringUtil.IsNullOrEmpty(sortf) && !StringUtil.IsNullOrEmpty(sortd)) {
                    orderstr.append(" order by " + sortf + " " + sortd);
                }
            } else {
                String jsons = request.getParameter("sort");
                ToolHelper.DeserializeExtJsSortInfo(jsons, orderstr);
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("系统异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/GetMDLList", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetMDLList", notes = "获取当前模块配置列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetMDLList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();

        //参数获取
        String tmpstr = "";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = StringUtil.IsNullOrEmpty(starts) ? 0 : Integer.parseInt(starts);
        int limit = StringUtil.IsNullOrEmpty(limits) ? 20 : Integer.parseInt(limits);

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        String strsql = "";
        int rcount = 0;

        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return ajaxResult;
        }

        try {
            String rcsql = DBHelper.ToRecordCountSql(basesql.toString());
            tmpstr = sqlhelper.ExecuteScalar(rcsql);
            if (!tmpstr.equals("")) {
                rcount = Integer.parseInt(tmpstr);
            }

            int pageCount = (rcount / limit) + 1;
            int currpage = start / limit;
            strsql = sqlhelper.ToPageSql(basesql.toString(), orderstr.toString(), limit, currpage);

            List<Module> list = sqlhelper.GetObjectList(Module.class, strsql);
            ajaxResult = AjaxResult.extgrid(Module.class, rcount, list);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/GetMDLById", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetMDLById", notes = "获取指定具有指定ID的模块配置接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetMDLById(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();
        String Id = request.getParameter("ID");
        if (StringUtil.IsNullOrEmpty(Id)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            String strsql = "select * from Module where ID='" + Id + "'";
            Module obj = sqlhelper.GetObject(Module.class, strsql);
            ajaxResult = AjaxResult.extform(Module.class, "获取信息成功！", obj);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ExportExcel", produces = {"text/plain;charset=UTF-8"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ExportExcel", notes = "导出当前模块配置信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String ExportExcel(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return JSON.toJSON(ajaxResult).toString();
        }

        try {
            StringBuilder fname = new StringBuilder();
            StringBuilder retstr = new StringBuilder();
            DataTable dt = sqlhelper.GetDataTable(basesql.toString() + " " + orderstr.toString());
            List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<ToolHelper.ExportColumnMode>();
            cmlist.add(new ToolHelper.ExportColumnMode("ID", "ID", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("module_num", "module_num", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("module_name", "module_name", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("module_type", "module_type", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("is_show", "is_show", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("url", "url", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("define_f", "define_f", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("define_s", "define_s", 20));

            boolean retb = ToolHelper.ExportDS2XlsFile(dt, "模块配置信息", cmlist, retstr, fname);
            if (retb) {
                ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
                cfs.success = true;
                cfs.text = "生成文件成功！";
                cfs.fname = fname.toString();
                cfs.data1 = ToolHelper.File2Bytes(ConfigHelper.getTempPath() + ConfigHelper.getfSepChar() + fname.toString());
                cfs.data2 = null;

                String jsonstr = JSON.toJSON(cfs).toString();
                return jsonstr;
            } else {
                ajaxResult = AjaxResult.error("导出文件异常!");
                return JSON.toJSON(ajaxResult).toString();
            }
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }

    @RequestMapping(value = "/GetModuleList", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetModuleList", notes = "获取当前模块配置列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetModuleList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();
        String strsql = "";

        try {
            strsql = "select * from module order by id";
            List<Module> list = sqlhelper.GetObjectList(Module.class, strsql);
            ajaxResult = AjaxResult.extgrid(Module.class, list.size(), list);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

}
