package com.soft.framework.common.utils.prop;

import java.io.*;
import java.util.Properties;

public class PropUtil {
    private String properiesName = "";
    private Properties pro = null;

    public PropUtil() {
    }

    public PropUtil(String fileName) {
        this.properiesName = fileName;
        LoadProperties();
    }

    public void LoadProperties() {
        FileInputStream is = null;
        InputStreamReader isr = null;
        try {
            File file = new File(properiesName);
            if (file.exists()) {
                is = new FileInputStream(properiesName);
                isr = new InputStreamReader(is, "UTF-8");
                BufferedReader br = new BufferedReader(isr);
                pro = new Properties();
                pro.load(br);

                if (isr != null) {
                    isr.close();
                }
                if (is != null) {
                    is.close();
                }
            }
        } catch (Exception Ex) {
            try {
                if (isr != null) {
                    isr.close();
                }
                if (is != null) {
                    is.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public String readString(String key) {
        if (pro == null) {
            return "";
        }
        String rets = pro.getProperty(key);
        if (rets == null) {
            return "";
        } else {
            return rets;
        }
    }

    public String readString(String key, String defval) {
        if (pro == null) {
            return defval;
        }
        String rets = pro.getProperty(key);
        if (rets == null) {
            return defval;
        } else {
            return rets;
        }
    }

    public boolean readBoolean(String key) {
        if (pro == null) {
            return false;
        }
        String rets = pro.getProperty(key);
        if (rets == null) {
            return false;
        } else {
            return Boolean.valueOf(rets);
        }
    }

    public boolean readBoolean(String key, boolean defval) {
        if (pro == null) {
            return defval;
        }
        String rets = pro.getProperty(key);
        if (rets == null) {
            return defval;
        } else {
            return Boolean.valueOf(rets);
        }
    }

    public Integer readInteger(String key) {
        if (pro == null) {
            return -1;
        }
        String rets = pro.getProperty(key);
        if (rets == null) {
            return -1;
        } else {
            return Integer.valueOf(rets);
        }
    }

    public Integer readInteger(String key, int defval) {
        if (pro == null) {
            return defval;
        }
        String rets = pro.getProperty(key);
        if (rets == null) {
            return defval;
        } else {
            return Integer.valueOf(rets);
        }
    }

    public void writeString(String key, String value) {
        pro.setProperty(key, String.valueOf(value));
    }

    public void writeBoolean(String key, Boolean value) {
        writeString(key, value.toString());
    }

    public void writeIntger(String key, int value) {
        writeString(key, new Integer(value).toString());
    }

    public void writeFile() {
        FileOutputStream os = null;
        try {
            os = new FileOutputStream(properiesName);
            OutputStreamWriter osw = new OutputStreamWriter(os, "UTF-8");
            pro.store(osw, "");
            os.flush();
            os.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != os) {
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
