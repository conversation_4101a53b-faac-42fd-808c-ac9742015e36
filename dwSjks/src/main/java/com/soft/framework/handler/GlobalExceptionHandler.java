package com.soft.framework.handler;

import com.soft.framework.common.constant.HttpStatus;
import com.soft.framework.common.exception.BaseException;
import com.soft.framework.common.exception.CustomException;
import com.soft.framework.common.exception.DemoModeException;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import org.apache.catalina.connector.ClientAbortException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.AccountExpiredException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletRequest;

/**
 * 全局异常处理器
 * 
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler
{
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 基础异常
     */
    @ExceptionHandler(BaseException.class)
    public AjaxResult baseException(BaseException e)
    {
        return AjaxResult.error(e.getMessage());
    }

    /**
     * 业务异常
     */
    @ExceptionHandler(CustomException.class)
    public AjaxResult businessException(HttpServletRequest req,CustomException e)
    {
        if (StringUtil.isNull(e.getCode()))
        {
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.error(e.getCode(),"错误地址url,"+req.getRequestURL()+",错误信息："+e.getMessage());
    }

    @ExceptionHandler(NoHandlerFoundException.class)
    public AjaxResult handlerNoFoundException(HttpServletRequest req,NoHandlerFoundException e)
    {
        log.error(e.getMessage(), e);
        return AjaxResult.error(HttpStatus.NOT_FOUND, "错误地址url,"+req.getRequestURL()+",路径不存在，请检查路径是否正确");
    }

    @ExceptionHandler(AccessDeniedException.class)
    public AjaxResult handleAuthorizationException(HttpServletRequest req, AccessDeniedException e)
    {
        log.error(e.getMessage());
        return AjaxResult.error(HttpStatus.FORBIDDEN, "错误地址url,"+req.getRequestURL()+",没有权限，请联系管理员授权");
    }

    @ExceptionHandler(AccountExpiredException.class)
    public AjaxResult handleAccountExpiredException(HttpServletRequest req, AccountExpiredException e)
    {
        log.error(e.getMessage(), e);
        return AjaxResult.error("错误地址url,"+req.getRequestURL()+",错误信息："+e.getMessage());
    }

    @ExceptionHandler(UsernameNotFoundException.class)
    public AjaxResult handleUsernameNotFoundException(HttpServletRequest req, UsernameNotFoundException e)
    {
        log.error(e.getMessage(), e);
        return AjaxResult.error("错误地址url,"+req.getRequestURL()+",错误信息："+e.getMessage());
    }

    @ExceptionHandler(ClientAbortException.class)
    public AjaxResult handleAccountExpiredException(HttpServletRequest req, ClientAbortException e)
    {
        log.error(e.getMessage(), e);
        return AjaxResult.error("错误地址url,"+req.getRequestURL()+",错误信息："+e.getMessage());
    }

    @ExceptionHandler(Exception.class)
    public AjaxResult handleException(HttpServletRequest req, Exception e)
    {
        log.error(e.getMessage(), e);
        return AjaxResult.error("错误地址url,"+req.getRequestURL()+",错误信息："+e.getMessage());
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(BindException.class)
    public AjaxResult validatedBindException(HttpServletRequest req,BindException e)
    {
        log.error(e.getMessage(), e);
        String message = e.getAllErrors().get(0).getDefaultMessage();
        return AjaxResult.error("错误地址url,"+req.getRequestURL()+",错误信息："+e.getMessage());
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Object validExceptionHandler(HttpServletRequest req,MethodArgumentNotValidException e)
    {
        log.error(e.getMessage(), e);
        String message = e.getBindingResult().getFieldError().getDefaultMessage();
        return AjaxResult.error("错误地址url,"+req.getRequestURL()+",错误信息："+e.getMessage());
    }

    /**
     * 演示模式异常
     */
    @ExceptionHandler(DemoModeException.class)
    public AjaxResult demoModeException(HttpServletRequest req,DemoModeException e)
    {
        return AjaxResult.error("错误地址url,"+req.getRequestURL()+",演示模式，不允许操作");
    }
}
