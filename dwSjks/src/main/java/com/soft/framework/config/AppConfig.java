package com.soft.framework.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * 读取项目相关配置
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "server")
public class AppConfig {
    public static Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        AppConfig.port = port;
    }

    @Value("${server.port}")
    private static Integer port;
}
