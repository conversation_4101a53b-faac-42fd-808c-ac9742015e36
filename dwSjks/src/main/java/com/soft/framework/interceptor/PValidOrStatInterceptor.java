package com.soft.framework.interceptor;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.ip.IpUtils;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.LogHelper;
import com.soft.framework.helper.SecToolHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Enumeration;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/***
 * 拦截器,单例
 */
@Component
public class PValidOrStatInterceptor implements HandlerInterceptor {
    private static final Logger logger = LoggerFactory.getLogger(PValidOrStatInterceptor.class);

    public boolean preHandle(HttpServletRequest req, HttpServletResponse resp, Object arg2) {
        long begin_nao_time = System.currentTimeMillis();
        String realIp = IpUtils.getIpAddr(req);
        req.setAttribute("p_real_ip", realIp);
        req.setAttribute("begin_nao_time", begin_nao_time);

        //如果request存在sql注入，终止访问
        if(isRequestSqlInject(req))
        {
            ResponseSqlInjectInfo(resp);
            return false;
        }

        String uri = req.getRequestURI();
        logger.debug(uri+"调用开始...");
        return true;
    }

    public void afterCompletion(HttpServletRequest req, HttpServletResponse resp, Object arg2, Exception arg3) throws Exception {
        long begin_time = (Long) req.getAttribute("begin_nao_time");
        String real_ip = (String) req.getAttribute("p_real_ip");
        long interval = System.currentTimeMillis() - begin_time;
        long intsenconds= TimeUnit.MILLISECONDS.toSeconds(interval);
        String uri = req.getRequestURI();
        logger.debug(uri+"调用结束...");
        if(intsenconds>5)  //仅记录运行时间超出5秒的接口调用
        {
            SimpleDateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String sdate= dateformat.format(begin_time);
            String edate=dateformat.format(System.currentTimeMillis());
            LogHelper.WriteOCCILog(real_ip+"|"+uri+"|"+sdate+"|"+edate+"|"+intsenconds);
        }
    }

    public void ResponseSqlInjectInfo(HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        PrintWriter out = null;
        try {
            out = response.getWriter();
            AjaxResult ajaxResult=AjaxResult.error("检测到sql注入威胁，终止操作",-1,false);
            out.append(JSON.toJSON(ajaxResult).toString());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                out.close();
            }
        }
    }

    public boolean isRequestSqlInject(HttpServletRequest request) {
        //QueryString携带参数
        //检测paramterMap
        Map<String, String[]> map1 = request.getParameterMap();
        Set<String> keys = map1.keySet();
        for (String key : keys) {
            String[] valArr = map1.get(key);
            if (valArr != null && valArr.length > 0) {
                for (int i = 0; i < valArr.length; i++) {
                    String tmpval = valArr[i].trim();
                    if(SecToolHelper.SQL_Injection_Detection(request,key,tmpval))
                    {
                        return true;
                    }
                }
            }
        }

        //form表单参数
        //ParameterNames
        Enumeration pNames=request.getParameterNames();
        while(pNames.hasMoreElements()){
            String name=(String)pNames.nextElement();
            String value=request.getParameter(name);
            if(SecToolHelper.SQL_Injection_Detection(request,name,value))
            {
                return true;
            }
        }

        //form文件参数
        //ParameterNames
        if (request instanceof MultipartHttpServletRequest)
        {
            Map<String, MultipartFile> fmap=((MultipartHttpServletRequest)request).getFileMap();
            Set<String> keyf = fmap.keySet();
            for (String key : keyf) {
                MultipartFile valf = fmap.get(key);
                if (valf != null) {
                    String tmpval = valf.getOriginalFilename().trim();
                    if(SecToolHelper.FILE_Injection_Detection(request,tmpval))
                    {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    public void postHandle(HttpServletRequest req, HttpServletResponse resp, Object arg2, ModelAndView arg3)throws Exception {

    }
}
