package com.soft.gcc.xtbg.sjks.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.sjks.domain.SjKsQrcode;
import com.soft.gcc.xtbg.sjks.domain.SjKsQrcodeDetail;

/**
* <AUTHOR>
* @description 针对表【sj_ks_qrcode_detail(设计-考试-二维码子表)】的数据库操作Service
* @createDate 2025-07-30 14:15:09
*/
public interface SjKsQrcodeDetailService extends IService<SjKsQrcodeDetail> {

    /**
     * 获取详情列表（原有方法，显示所有详情）
     */
    IPage<SjKsQrcodeDetail> getList(SjKsQrcode sjKsQrcode);

    /**
     * 根据主表ID获取详情列表（二级列表）
     * @param sjKsQrcodeDetail 查询条件，包含qrCodeId
     * @return 分页结果
     */
    IPage<SjKsQrcodeDetail> getDetailList(SjKsQrcodeDetail sjKsQrcodeDetail);
}
