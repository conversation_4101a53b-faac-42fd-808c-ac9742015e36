package com.soft.gcc.xtbg.sjks.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.sjks.domain.SjKsQrcode;
import com.yyszc.wpbase.ventity.PersonEntity;

/**
* <AUTHOR>
* @description 针对表【sj_ks_qrcode(设计-考试-二维码主表)】的数据库操作Service
* @createDate 2025-07-30 14:15:09
*/
public interface SjKsQrcodeService extends IService<SjKsQrcode> {

    /**
     * 生成二维码并导出PDF
     * @param quantity 生成数量
     * @return PDF文件字节数组
     */
    byte[] generateQrcodeAndExportPdf(Integer quantity, PersonEntity user);

    /**
     * 获取主表列表（一级列表）
     * @param sjKsQrcode 查询条件
     * @return 分页结果
     */
    IPage<SjKsQrcode> getList(SjKsQrcode sjKsQrcode);

    /**
     * 根据主表ID导出PDF
     * @param qrCodeId 主表ID
     * @return PDF文件字节数组
     */
    byte[] exportPdfByQrCodeId(Integer qrCodeId);
}
