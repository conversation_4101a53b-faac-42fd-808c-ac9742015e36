package com.soft.gcc.xtbg.sjks.service.impl;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.sjks.domain.SjKsQrcode;
import com.soft.gcc.xtbg.sjks.domain.SjKsQrcodeDetail;
import com.soft.gcc.xtbg.sjks.mapper.SjKsQrcodeDetailMapper;
import com.soft.gcc.xtbg.sjks.mapper.SjKsQrcodeMapper;
import com.soft.gcc.xtbg.sjks.service.SjKsQrcodeDetailService;
import com.soft.gcc.xtbg.sjks.service.SjKsQrcodeService;
import com.soft.gcc.xtbg.sjks.util.QRCodeGenerator;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Random;
import java.util.Set;

// PDF相关导入
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.pdf.PdfWriter;
import com.google.zxing.WriterException;

import javax.imageio.ImageIO;

/**
* <AUTHOR>
* @description 针对表【sj_ks_qrcode(设计-考试-二维码主表)】的数据库操作Service实现
* @createDate 2025-07-30 14:15:09
*/
@Service
@Slave
public class SjKsQrcodeServiceImpl extends ServiceImpl<SjKsQrcodeMapper, SjKsQrcode> implements SjKsQrcodeService{

    @Resource
    private SjKsQrcodeDetailService sjKsQrcodeDetailService;

    @Resource
    private SjKsQrcodeDetailMapper sjKsQrcodeDetailMapper;

    @Override
    public byte[] generateQrcodeAndExportPdf(Integer quantity, PersonEntity user) {
        System.out.println("Service: 开始生成二维码，数量=" + quantity + ", 用户ID=" + user.getId());

        Date now = new Date();
        int currentYear = now.getYear() + 1900; // Date.getYear() returns year - 1900
        int currentMonth = now.getMonth() + 1;   // Date.getMonth() returns 0-11

        System.out.println("Service: 当前年月=" + currentYear + "-" + currentMonth);

        // 查询当前年月的最大批次
        Integer maxBatch = getMaxBatchByYearMonth(currentYear, currentMonth);
        int newBatch = maxBatch == null ? 1 : maxBatch + 1;


        // 插入主表记录
        SjKsQrcode qrcode = new SjKsQrcode();
        qrcode.setYear(currentYear);
        qrcode.setMonth(currentMonth);
        qrcode.setBatch(newBatch);
        qrcode.setQrCodeNumber(quantity);
        qrcode.setCreateBy(String.valueOf(user.getId()));
        qrcode.setCreateTime(now);
        qrcode.setDeleted("0");

        // 保存并确保获取到ID
        baseMapper.insert(qrcode);

        // 如果ID仍然为null，手动查询获取
        if (qrcode.getId() == null) {
            System.out.println("Service: ID为null，尝试手动查询获取ID");
            LambdaQueryWrapper<SjKsQrcode> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SjKsQrcode::getYear, currentYear)
                   .eq(SjKsQrcode::getMonth, currentMonth)
                   .eq(SjKsQrcode::getBatch, newBatch)
                   .eq(SjKsQrcode::getDeleted, "0")
                   .orderByDesc(SjKsQrcode::getCreateTime);

            List<SjKsQrcode> records = list(wrapper);
            if (records != null && !records.isEmpty()) {
                qrcode.setId(records.get(0).getId());
                System.out.println("Service: 手动获取到ID=" + qrcode.getId());
            }
        }

        // 生成详情记录
        List<SjKsQrcodeDetail> detailList = generateQrcodeDetails(qrcode.getId(), currentYear, currentMonth, newBatch, quantity, user);

        // 分批插入，每次最多100条
        batchInsertDetails(detailList);


        // 直接生成并返回PDF
        try {
            byte[] pdfBytes = generatePdfWithQrcodes(detailList);
            return pdfBytes;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("生成PDF失败：" + e.getMessage(), e);
        }
    }

    /**
     * 查询指定年月的最大批次
     */
    private Integer getMaxBatchByYearMonth(int year, int month) {
        LambdaQueryWrapper<SjKsQrcode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SjKsQrcode::getYear, year)
               .eq(SjKsQrcode::getMonth, month)
               .eq(SjKsQrcode::getDeleted, "0")
               .orderByDesc(SjKsQrcode::getBatch);

        // 使用MyBatis Plus的分页查询，取第一条记录
        List<SjKsQrcode> records = list(wrapper);
        if (records != null && !records.isEmpty()) {
            return records.get(0).getBatch();
        }
        return null;
    }

    /**
     * 生成二维码详情记录
     */
    private List<SjKsQrcodeDetail> generateQrcodeDetails(Integer qrCodeId, int year, int month, int batch, int quantity,PersonEntity user) {
        List<SjKsQrcodeDetail> detailList = new ArrayList<>();
        Set<String> generatedRandomCodes = new HashSet<>(); // 只存储6位随机数，避免重复
        Date now = new Date();

        // 格式化年份和月份
        String yearStr = String.format("%02d", year % 100); // 取年份后两位
        String monthStr = String.format("%02d", month);
        String batchStr = String.format("%02d", batch);

        for (int i = 0; i < quantity; i++) {
            String randomStr;
            do {
                // 生成6位随机数字+大写字母
                randomStr = generateRandomString(6);
            } while (generatedRandomCodes.contains(randomStr)); // 只检查本次生成的随机数是否重复

            generatedRandomCodes.add(randomStr);

            // 组装完整的二维码
            String qrCode = yearStr + monthStr + randomStr + batchStr;

            SjKsQrcodeDetail detail = new SjKsQrcodeDetail();
            detail.setQrCodeId(qrCodeId);
            detail.setQrCode(qrCode);
            detail.setCreateBy(String.valueOf(user.getId()));
            detail.setCreateTime(now);
            detail.setDeleted("0");
            detailList.add(detail);
        }

        return detailList;
    }

    /**
     * 分批插入详情记录
     */
    private void batchInsertDetails(List<SjKsQrcodeDetail> detailList) {
        if (detailList == null || detailList.isEmpty()) {
            return;
        }
        //每次插入的条数
        int batchSize = 300;
        int totalSize = detailList.size();

        for (int i = 0; i < totalSize; i += batchSize) {
            int endIndex = Math.min(i + batchSize, totalSize);
            List<SjKsQrcodeDetail> batchList = detailList.subList(i, endIndex);

            sjKsQrcodeDetailMapper.batchInsert(batchList);
        }
    }

    /**
     * 生成随机字符串（数字+大写字母）
     */
    private String generateRandomString(int length) {
        String chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }

        return sb.toString();
    }





    /**
     * 生成包含二维码的PDF - 每页一个二维码，自定义页面大小230*150
     */
    private byte[] generatePdfWithQrcodes(List<SjKsQrcodeDetail> detailList) throws DocumentException, IOException, WriterException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        // 自定义页面大小（单位：磅，1磅≈0.35毫米）
        float pageWidth = 230f;   // 页面宽度
        float pageHeight = 150f;  // 页面高度

        // 二维码尺寸（适应页面大小，留一些边距）
        float margin = 10f; // 页面边距
        float availableWidth = pageWidth - 2 * margin;
        float availableHeight = pageHeight - 2 * margin;

        // 二维码使用较小的尺寸以适应页面
        float qrCodeSize = Math.min(availableWidth, availableHeight);
        int qrCodePixelSize = (int) qrCodeSize; // 像素大小

        // 创建自定义页面大小的文档
        Document document = new Document(new com.itextpdf.text.Rectangle(pageWidth, pageHeight));
        PdfWriter.getInstance(document, baos);

        document.open();

        for (int i = 0; i < detailList.size(); i++) {
            SjKsQrcodeDetail detail = detailList.get(i);

            // 除了第一页，其他都需要新建页面
            if (i > 0) {
                document.newPage();
            }

            // 生成二维码图片
            BufferedImage qrImage = QRCodeGenerator.generateQRCodeImage(detail.getQrCode(), qrCodePixelSize, qrCodePixelSize);

            // 转换为iText Image
            ByteArrayOutputStream imageStream = new ByteArrayOutputStream();
            ImageIO.write(qrImage, "PNG", imageStream);
            Image pdfImage = Image.getInstance(imageStream.toByteArray());

            // 计算二维码居中位置
            float x = (pageWidth - qrCodeSize) / 2;  // 水平居中
            float y = (pageHeight - qrCodeSize) / 2; // 垂直居中

            // 设置图片位置和大小
            pdfImage.setAbsolutePosition(x, y);
            pdfImage.scaleAbsolute(qrCodeSize, qrCodeSize);

            // 添加到文档
            document.add(pdfImage);
        }

        document.close();
        return baos.toByteArray();
    }

    @Override
    public IPage<SjKsQrcode> getList(SjKsQrcode sjKsQrcode) {
        IPage<SjKsQrcode> page = new Page<>(sjKsQrcode.getPageNum(), sjKsQrcode.getPageSize());
        LambdaQueryWrapper<SjKsQrcode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SjKsQrcode::getDeleted, "0")
               .orderByDesc(SjKsQrcode::getCreateTime);
        return page(page, wrapper);
    }

    @Override
    public byte[] exportPdfByQrCodeId(Integer qrCodeId) {
        if (qrCodeId == null) {
            throw new RuntimeException("主表ID不能为空");
        }

        // 查询该主表ID下的所有详情记录
        LambdaQueryWrapper<SjKsQrcodeDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SjKsQrcodeDetail::getQrCodeId, qrCodeId)
               .eq(SjKsQrcodeDetail::getDeleted, "0")
               .orderByDesc(SjKsQrcodeDetail::getId);

        List<SjKsQrcodeDetail> detailList = sjKsQrcodeDetailService.list(wrapper);

        if (detailList == null || detailList.isEmpty()) {
            throw new RuntimeException("未找到相关的二维码数据");
        }

        // 生成PDF
        try {
            return generatePdfWithQrcodes(detailList);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("生成PDF失败：" + e.getMessage(), e);
        }
    }
}




