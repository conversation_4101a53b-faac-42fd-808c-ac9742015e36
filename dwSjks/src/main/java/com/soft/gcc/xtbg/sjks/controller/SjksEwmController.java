package com.soft.gcc.xtbg.sjks.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.sjks.domain.SjKsQrcode;
import com.soft.gcc.xtbg.sjks.domain.SjKsQrcodeDetail;
import com.soft.gcc.xtbg.sjks.params.GenerateQrcodeParams;
import com.soft.gcc.xtbg.sjks.service.SjKsQrcodeDetailService;
import com.soft.gcc.xtbg.sjks.service.SjKsQrcodeService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 * @date 2025/7/30
 */
@RequestMapping("/sjks/ewm")
@RestController
public class SjksEwmController extends BaseController {

    @Resource
    private SjKsQrcodeDetailService sjKsQrcodeDetailService;

    @Resource
    private SjKsQrcodeService sjKsQrcodeService;

    /**
     * 获取主表列表（一级列表）
     */
    @PostMapping("/getMainList")
    public Result<Object> getMainList(@RequestBody SjKsQrcode sjKsQrcode) {
        return Result.ok(sjKsQrcodeService.getMainList(sjKsQrcode));
    }

    /**
     * 获取详情列表（二级列表）
     */
    @PostMapping("/getDetailList")
    public Result<Object> getDetailList(@RequestBody SjKsQrcodeDetail sjKsQrcodeDetail) {
        return Result.ok(sjKsQrcodeDetailService.getDetailList(sjKsQrcodeDetail));
    }

    /**
     * 生成二维码并导出PDF
     */
    @PostMapping("/generate")
    public void generateQrcode(@Valid @RequestBody GenerateQrcodeParams params, HttpServletResponse response) throws IOException {
        PersonEntity persen = user();
        if (persen == null) {
            throw new RuntimeException("用户未登录");
        }

        byte[] pdfBytes = sjKsQrcodeService.generateQrcodeAndExportPdf(params.getQuantity(), persen);

        String fileName = "二维码_" + System.currentTimeMillis() + ".pdf";
        response.setContentType("application/pdf");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

        response.getOutputStream().write(pdfBytes);
        response.getOutputStream().flush();
    }

    /**
     * 根据主表ID导出PDF
     */
    @PostMapping("/exportPdf/{qrCodeId}")
    public void exportPdfByQrCodeId(@PathVariable Integer qrCodeId, HttpServletResponse response) throws IOException {
        PersonEntity persen = user();
        if (persen == null) {
            throw new RuntimeException("用户未登录");
        }

        byte[] pdfBytes = sjKsQrcodeService.exportPdfByQrCodeId(qrCodeId);

        String fileName = "二维码导出_" + System.currentTimeMillis() + ".pdf";
        response.setContentType("application/pdf");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

        response.getOutputStream().write(pdfBytes);
        response.getOutputStream().flush();
    }

}
