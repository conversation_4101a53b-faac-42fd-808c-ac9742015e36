package com.soft.gcc.xtbg.sjks.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.sjks.domain.SjKsQrcode;
import com.soft.gcc.xtbg.sjks.domain.SjKsQrcodeDetail;
import com.soft.gcc.xtbg.sjks.mapper.SjKsQrcodeDetailMapper;
import com.soft.gcc.xtbg.sjks.service.SjKsQrcodeDetailService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【sj_ks_qrcode_detail(设计-考试-二维码子表)】的数据库操作Service实现
 * @createDate 2025-07-30 14:15:09
 */
@Service
public class SjKsQrcodeDetailServiceImpl extends ServiceImpl<SjKsQrcodeDetailMapper, SjKsQrcodeDetail>
        implements SjKsQrcodeDetailService {


    @Override
    public IPage<SjKsQrcodeDetail> getDetailList(SjKsQrcodeDetail sjKsQrcodeDetail) {
        IPage<SjKsQrcodeDetail> page = new Page<>(sjKsQrcodeDetail.getPageNum(), sjKsQrcodeDetail.getPageSize());
        LambdaQueryWrapper<SjKsQrcodeDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SjKsQrcodeDetail::getDeleted, "0");

        // 如果传入了qrCodeId，则按qrCodeId过滤
        if (sjKsQrcodeDetail.getQrCodeId() != null) {
            wrapper.eq(SjKsQrcodeDetail::getQrCodeId, sjKsQrcodeDetail.getQrCodeId());
        }

        wrapper.orderByDesc(SjKsQrcodeDetail::getId);
        return page(page, wrapper);
    }
}




