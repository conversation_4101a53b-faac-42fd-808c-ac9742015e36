package com.soft.gcc.common.t_smssend.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName T_SMSSend
 */
@TableName(value ="T_SMSSend")
@Data
public class TSmssend implements Serializable {
    /**
     * 系统来源
     */
    @TableId("Source")
    private String source;

    /**
     * 手机号 多个手机号中间有逗号分割
     */
    @TableField(value = "Phone")
    private String phone;

    /**
     * 发送时间
     */
    @TableField(value = "DateTime")
    private Date dateTime;

    /**
     * 序号
     */
    @TableField(value = "Sequence")
    private Integer sequence;

    /**
     * 是否已发送
     */
    @TableField(value = "Result")
    private Boolean result;

    /**
     * 短信内容
     */
    @TableField(value = "Info")
    private String info;

    /**
     *
     */
    @TableField(value = "yzm")
    private String yzm;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
