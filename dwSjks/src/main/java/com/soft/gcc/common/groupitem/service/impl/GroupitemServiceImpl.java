package com.soft.gcc.common.groupitem.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.common.groupitem.entity.Groupitem;
import com.soft.gcc.common.groupitem.mapper.GroupitemMapper;
import com.soft.gcc.common.groupitem.service.GroupitemService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【GroupItem】的数据库操作Service实现
* @createDate 2023-02-28 15:10:35
*/
@Service
public class GroupitemServiceImpl extends ServiceImpl<GroupitemMapper, Groupitem>
    implements GroupitemService{

}




