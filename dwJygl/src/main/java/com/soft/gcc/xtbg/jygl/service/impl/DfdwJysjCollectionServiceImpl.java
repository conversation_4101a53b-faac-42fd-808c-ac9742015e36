package com.soft.gcc.xtbg.jygl.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itextpdf.text.pdf.BaseFont;
import com.soft.framework.helper.SessionHelper;
import com.soft.gcc.common.dfdw_dict.entity.DfdwTDictData;
import com.soft.gcc.common.dfdw_dict.service.DfdwTDictDataService;
import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.common.t_file.service.TFileService;
import com.soft.gcc.xtbg.base.util.MockMultipartFile;
import com.soft.gcc.xtbg.jygl.entity.DfdwJysjAssociated;
import com.soft.gcc.xtbg.jygl.entity.DfdwJysjBusinessData;
import com.soft.gcc.xtbg.jygl.entity.DfdwJysjCollection;
import com.soft.gcc.xtbg.jygl.entity.DfdwJysjDebtmanagement;
import com.soft.gcc.xtbg.jygl.mapper.DfdwJysjBusinessDataMapper;
import com.soft.gcc.xtbg.jygl.mapper.DfdwJysjCollectionMapper;
import com.soft.gcc.xtbg.jygl.params.DfdwJysjCollectionParams;
import com.soft.gcc.xtbg.jygl.service.DfdwJysjAssociatedService;
import com.soft.gcc.xtbg.jygl.service.DfdwJysjBusinessDataService;
import com.soft.gcc.xtbg.jygl.service.DfdwJysjCollectionService;
import com.soft.gcc.xtbg.jygl.service.DfdwJysjDebtmanagementService;
import com.soft.gcc.xtbg.jygl.util.AliyunOSSUtils;
import com.soft.gcc.xtbg.jygl.util.MailUtil;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.mock.web.MockMultipartFile;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

//import com.itextpdf.text.BaseFont;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfPCell;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSectPr;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import java.util.List;
import java.util.Properties;

/**
* <AUTHOR>
* @description 针对表【DFDW_JYSJ_Collection(经营数据-催收管理)】的数据库操作Service实现
* @createDate 2025-07-15 14:13:11
*/
@Service
public class DfdwJysjCollectionServiceImpl extends ServiceImpl<DfdwJysjCollectionMapper, DfdwJysjCollection>
    implements DfdwJysjCollectionService{

    @Autowired
    private DfdwJysjBusinessDataService businessDataService;

    @Autowired
    private DfdwJysjAssociatedService associatedService;
    @Autowired
    private DfdwTDictDataService dfdwTDictDataService;
    @Autowired
    private DfdwJysjDebtmanagementService dfdwJysjDebtmanagementService;

    @Autowired
    private MailUtil mailUtil;
    @Resource
    TFileService fileService;

    @Override
    public IPage<DfdwJysjCollection> pageList(DfdwJysjCollectionParams param) {

        return this.baseMapper.pageList(new Page<DfdwJysjCollection>(param.getPageNum(), param.getPageSize()), param);

        //return this.baseMapper.pageList1(new Page<DfdwJysjCollection>(param.getPageNum(), param.getPageSize()), param);
    }

    

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DfdwJysjCollection addOrUpdate(DfdwJysjCollection data)  {

        if(data.getId() != null){
            data.setUpdateTime(new Date());
            data.setUpdateBy( SessionHelper.getSessionPerson().getRealName());
            //如果上传了  法律文书、法院起诉文书等材料  相关项目的是否涉诉变为是 
            if(StringUtils.isNotBlank(data.getSupportDocsIds()) && data.getSupportDocsIds().length() > 0){
                businessDataService.update(new LambdaUpdateWrapper<DfdwJysjBusinessData>()
                        .set(DfdwJysjBusinessData::getIsInLawsuit,1)
                        .eq(DfdwJysjBusinessData::getId,data.getBusinessDataId())
                );
            }
            
            this.baseMapper.updateById(data);
            return data;
        }
        data.setEmailStatus(0);
        data.setConfirmationStatus(0);
        data.setState(0);
        //处理催收期 根据项目名称获取表中是否已经存在数据
        //获取最新一期的数据
        List<DfdwJysjCollection> list = this.baseMapper.selectList(new LambdaQueryWrapper<DfdwJysjCollection>()
                .eq(DfdwJysjCollection::getBusinessDataId,data.getBusinessDataId())
                .orderByDesc(DfdwJysjCollection ::getCreateTime)
        );
        if(list.isEmpty()){
            data.setNumberOfDunningPeriods("第一期");
        }else {
            //获取期数 并且转换成阿拉伯数字
            int periodNumber = getPeriodNumber(list.get(0).getNumberOfDunningPeriods());
            periodNumber++;
            data.setNumberOfDunningPeriods("第" + convertNumberToChinese(periodNumber) + "期");
        }
        data.setCreateTime(new Date());
        data.setCreateBy( SessionHelper.getSessionPerson().getRealName());
        this.baseMapper.insert(data);

        try{
            TFile file = null;
            if (data.getAnnexType() == 1){
                //处理文件 - 保存催办函
                file = fileService.uploadAndSaveFile(reminderLetter(data),"催办函.docx","jygl","1", Math.toIntExact(data.getId()));
            }
            if (data.getAnnexType() == 2){
                //处理文件 -工程审计请办函
                file = fileService.uploadAndSaveFile(auditRequestLetter(data),"工程审计请办函.docx","jygl","2", Math.toIntExact(data.getId()));
            }
            if (data.getAnnexType() == 3){
                //处理文件 - 催款函
                file = fileService.uploadAndSaveFile(paymentDemandLetter(data),"催款函.docx","jygl","3", Math.toIntExact(data.getId()));
            }
            if (data.getAnnexType() == 4){
                //处理文件 - 往来款项催告函
                file = fileService.uploadAndSaveFile(dunningLetter(data),"往来款项催告函.docx","jygl","4", Math.toIntExact(data.getId()));
            }

            //防止没有记录上用户自行上传的文件
            if(StringUtils.isNotBlank(data.getAnnexId())){
                if (file != null) {
                    data.setAnnexId(data.getAnnexId()+","+file.getId());
                }
            }else {
                if (file != null) {
                    data.setAnnexId(String.valueOf(file.getId()));
                }
            }

            this.baseMapper.updateById(data);
        }catch (Exception e){
            e.printStackTrace();
            throw new RuntimeException("生成催收文件失败: " + e.getMessage(), e);
        }

        return data;
    }


    @Override
    public void sendEmail(DfdwJysjCollectionParams param) {
        try {
            // 获取催收记录
            DfdwJysjCollection collection = this.baseMapper.selectById(param.getId());
            if (collection == null) {
                throw new RuntimeException("催收记录不存在");
            }
            String fileIds = collection.getAnnexId();
            String toEmail = "";
            // 邮件主题
            String subject = "";
            // 生成邮件内容
            String content = "";
            //判断类型 获取业务数据
            if(param.getType() == 1){
                // 获取关联的业务数据
                DfdwJysjBusinessData businessData = businessDataService.getById(collection.getBusinessDataId());
                if (businessData == null) {
                    throw new RuntimeException("关联的业务数据不存在");
                }
                //获取往来户信息，获取邮件地址准备发送邮件
                List<DfdwJysjAssociated> associatedList = associatedService.list(new LambdaQueryWrapper<DfdwJysjAssociated>().eq(DfdwJysjAssociated::getAssociatedUnits,businessData.getCustomer()));
                if(associatedList.isEmpty()){
                    throw new RuntimeException("客户信息不存在");
                }
                // 验证邮箱地址
                toEmail = associatedList.get(0).getEmail() != null ? associatedList.get(0).getEmail() : "<EMAIL>";
                // 邮件主题
                subject = "催收通知 - " + businessData.getProjectName();

                // 生成邮件内容
                content = generateTestTextContentBusiness(businessData);
            }
            //隐性债券
            if(param.getType() == 2){
                // 获取关联的业务数据
                DfdwJysjDebtmanagement businessData = dfdwJysjDebtmanagementService.getById(collection.getBusinessDataId());
                if (businessData == null) {
                    throw new RuntimeException("关联的业务数据不存在");
                }

                //获取往来户信息，获取邮件地址准备发送邮件
                List<DfdwJysjAssociated> associatedList = associatedService.list(new LambdaQueryWrapper<DfdwJysjAssociated>().eq(DfdwJysjAssociated::getAssociatedUnits,businessData.getCompanyName()));
                if(associatedList.isEmpty()){
                    throw new RuntimeException("客户信息不存在");
                }
                // 验证邮箱地址
                toEmail = associatedList.get(0).getEmail() != null ? associatedList.get(0).getEmail() : "<EMAIL>";
                // 邮件主题
                subject = "催收通知 - " + businessData.getProjectName();

                // 生成邮件内容 - 尝试从docx文件读取，如果没有则使用默认模板
                String docxFileId = findDocxFileId(fileIds);
                content = generateTestTextContentDebtmanagement(businessData, docxFileId);

            }


            System.out.println("开始发送测试邮件...");
            System.out.println("收件人: " + toEmail);
            System.out.println("主题: " + subject);

            // 处理附件
            List<File> attachments = processAttachments(fileIds, param.getIsZipCompressed());

            // 创建邮件发送器
            JavaMailSenderImpl mailSender = createMailSender();

            //todo:docx内容转邮件内容

            // 发送带附件的邮件
            if (attachments != null && !attachments.isEmpty()) {
                sendMailWithAttachments(mailSender, toEmail, subject, content, attachments);
            } else {
                // 创建邮件消息
                SimpleMailMessage message = new SimpleMailMessage();
                message.setFrom(mailSender.getUsername());
                message.setTo(toEmail);
                message.setSubject(subject);
                message.setText(content);
                mailSender.send(message);
            }
            //修改催收状态
            collection.setState(1);
            collection.setEmailStatus(1);
            this.updateById(collection);
            //System.out.println("邮件发送成功！");

        } catch (Exception e) {
            System.err.println("邮件发送失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建邮件发送器
     */
    private JavaMailSenderImpl createMailSender() {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        //读取字典设置的信息
        List<DfdwTDictData> dictList = dfdwTDictDataService.list(new LambdaQueryWrapper<DfdwTDictData>().eq(DfdwTDictData::getDictType,"dfdw_jysj_email_setting"));
        //读取字典设置的信息并转换为Map
        Map<String, String> emailConfig = dictList
                .stream()
                .collect(Collectors.toMap(DfdwTDictData::getLabel, DfdwTDictData::getValue));

        // 使用配置
        String host = emailConfig.get("邮件服务器host");
        String port = emailConfig.get("邮件服务器端口" );
        String username = emailConfig.get("邮件服务器用户名" );
        String password = emailConfig.get("邮件服务器密码");

        // 验证必要的配置参数
        if (host == null || host.trim().isEmpty()) {
            throw new RuntimeException("邮件服务器host配置不能为空");
        }
        if (port == null || port.trim().isEmpty()) {
            throw new RuntimeException("邮件服务器端口配置不能为空");
        }
        if (username == null || username.trim().isEmpty()) {
            throw new RuntimeException("邮件服务器用户配置不能为空");
        }
        if (password == null || password.trim().isEmpty()) {
            throw new RuntimeException("邮件服务器密码配置不能为空");
        }

        // 邮件服务器配置（请根据您的邮件服务器修改）
        mailSender.setHost(host.trim()); // 邮箱服务器
        mailSender.setPort(Integer.parseInt(port.trim())); // 端口号
        mailSender.setUsername(username.trim()); // 用户名
        mailSender.setPassword(password.trim()); // 密码或授权码

        // 邮件属性配置
        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "false");
        props.put("mail.debug", "true");

        return mailSender;
    }

    /**
     * 处理附件：从fileService获取文件，转换为PDF，可选压缩为ZIP
     */
    private List<File> processAttachments(String fileIds, Boolean isZipCompressed) {
        List<File> attachments = new ArrayList<>();

        try {
            // 获取文件列表
            List<TFile> fileList = fileService.list(new LambdaQueryWrapper<TFile>().in(TFile::getId, Arrays.asList(fileIds.split(","))));
            if (fileList == null || fileList.isEmpty()) {
                System.out.println("没有找到相关附件");
                return attachments;
            }

            List<File> processedFiles = new ArrayList<>();

            // 处理每个文件
            for (TFile tFile : fileList) {
                try {
                    // 从阿里云OSS读取文件字节数组
                    byte[] fileBytes = AliyunOSSUtils.downloadFileStream(tFile.getFilepath());
                    if (fileBytes == null || fileBytes.length == 0) {
                        System.out.println("无法读取文件: " + tFile.getFilename());
                        continue;
                    }

                    // 将字节数组转换为输入流
                    InputStream inputStream = new ByteArrayInputStream(fileBytes);

                    // 转换文件为PDF格式
                    File pdfFile = convertToPdf(inputStream, tFile.getFilename());
                    if (pdfFile != null) {
                        processedFiles.add(pdfFile);
                        System.out.println("成功转换文件为PDF: " + tFile.getFilename() + " -> " + pdfFile.getName());
                    }

                    inputStream.close();
                } catch (Exception e) {
                    System.err.println("处理文件失败: " + tFile.getFilename() + ", " + e.getMessage());
                    e.printStackTrace();
                }
            }

            // 根据参数决定是否压缩
            if (isZipCompressed != null && isZipCompressed && !processedFiles.isEmpty()) {
                // 压缩为ZIP文件
                File zipFile = createZipFile(processedFiles, "催收附件_" + System.currentTimeMillis() + ".zip");
                if (zipFile != null) {
                    attachments.add(zipFile);
                    System.out.println("创建ZIP文件: " + zipFile.getName());
                }
                // 清理临时文件
                for (File file : processedFiles) {
                    file.delete();
                }
            } else {
                // 不压缩，直接添加处理后的文件
                attachments.addAll(processedFiles);
            }

        } catch (Exception e) {
            System.err.println("处理附件失败: " + e.getMessage());
            e.printStackTrace();
        }

        return attachments;
    }

    /**
     * 生成测试邮件内容（普通文本版本）
     */
    private String generateTestTextContentBusiness(DfdwJysjBusinessData businessData) {
        StringBuilder content = new StringBuilder();

        // 标题
        content.append("催收通知函\n\n");

        // 收件人
        content.append(businessData != null ? businessData.getCompanyName() : "测试公司").append("：\n\n");

        // 正文第一段
        content.append("    因").append(businessData != null ? businessData.getProjectName() : "测试项目").append("项目，");
        content.append("本公司与贵公司/单位签订相关合同。");
        content.append("合同签订后，本公司已按约完成相关工作。\n\n");

        // 正文第二段
        content.append("    截止本函发送之日，贵公司/单位仍然未完成结算审计，");
        content.append("导致工程款项的结算支付无法完成。故，向贵公司/单位发送本函，");
        content.append("要求尽快完成结算审计工作。\n\n");

        // 落款
        content.append("                                        催告单位："+businessData.getCustomer()+"\n");
        content.append("                                        催告时间：").append(formatDate(new Date(), "yyyy年MM月dd日")).append("\n\n");

        // 页脚
        content.append("---\n");
        content.append("此邮件为系统自动发送，请勿直接回复。如有疑问，请联系相关业务人员。");

        return content.toString();
    }

    private String generateTestTextContentDebtmanagement(DfdwJysjDebtmanagement debtmanagement) {
        return generateTestTextContentDebtmanagement(debtmanagement, null);
    }

    /**
     * 生成债务管理邮件内容 - 支持从docx文件读取内容
     * @param debtmanagement 债务管理数据
     * @param docxFileId docx文件ID，如果为null则使用默认模板
     * @return 邮件内容
     */
    private String generateTestTextContentDebtmanagement(DfdwJysjDebtmanagement debtmanagement, String docxFileId) {
        // 如果提供了docx文件ID，则从OSS读取文件内容
        if (docxFileId != null && !docxFileId.trim().isEmpty()) {
            try {
                return generateEmailContentFromDocx(docxFileId, debtmanagement);
            } catch (Exception e) {
                System.err.println("从docx文件生成邮件内容失败，使用默认模板: " + e.getMessage());
                e.printStackTrace();
                // 如果读取docx失败，则使用默认模板
            }
        }

        // 默认模板内容
        StringBuilder content = new StringBuilder();

        // 标题
        content.append("催收通知函\n\n");

        // 收件人
        content.append(debtmanagement != null ? debtmanagement.getCompanyName() : "测试公司").append("：\n\n");

        // 正文第一段
        content.append("    因").append(debtmanagement != null ? debtmanagement.getProjectName() : "测试项目").append("项目，");
        content.append("本公司与贵公司/单位签订相关合同。");
        content.append("合同签订后，本公司已按约完成相关工作。\n\n");

        // 正文第二段
        content.append("    截止本函发送之日，贵公司/单位仍然未完成结算审计，");
        content.append("导致工程款项的结算支付无法完成。故，向贵公司/单位发送本函，");
        content.append("要求尽快完成结算审计工作。\n\n");

        // 落款
        content.append("                                        催告单位："+debtmanagement.getGroupName()+"\n");
        content.append("                                        催告时间：").append(formatDate(new Date(), "yyyy年MM月dd日")).append("\n\n");

        // 页脚
        content.append("---\n");
        content.append("此邮件为系统自动发送，请勿直接回复。如有疑问，请联系相关业务人员。");

        return content.toString();
    }

    /**
     * 从docx文件生成邮件内容
     * @param docxFileId docx文件ID
     * @param debtmanagement 债务管理数据，用于替换模板变量
     * @return 邮件内容
     */
    private String generateEmailContentFromDocx(String docxFileId, DfdwJysjDebtmanagement debtmanagement) throws Exception {
        // 根据文件ID获取文件信息
        TFile tFile = fileService.getById(docxFileId);
        if (tFile == null) {
            throw new RuntimeException("文件不存在，文件ID: " + docxFileId);
        }

        // 检查文件是否为docx格式
        if (!tFile.getFilename().toLowerCase().endsWith(".docx")) {
            throw new RuntimeException("文件格式不支持，只支持docx格式，当前文件: " + tFile.getFilename());
        }

        // 从阿里云OSS读取文件字节数组
        byte[] fileBytes = AliyunOSSUtils.downloadFileStream(tFile.getFilepath());
        if (fileBytes == null || fileBytes.length == 0) {
            throw new RuntimeException("无法从OSS读取文件: " + tFile.getFilename());
        }

        // 将字节数组转换为输入流
        try (InputStream inputStream = new ByteArrayInputStream(fileBytes)) {
            // 使用Apache POI读取DOCX文档
            XWPFDocument docxDocument = new XWPFDocument(inputStream);

            StringBuilder emailContent = new StringBuilder();

            // 按照文档中的顺序处理所有元素（段落和表格）
            List<IBodyElement> bodyElements = docxDocument.getBodyElements();

            for (IBodyElement element : bodyElements) {
                if (element instanceof XWPFParagraph) {
                    // 处理段落
                    XWPFParagraph paragraph = (XWPFParagraph) element;
                    String text = paragraph.getText();

                    if (text != null && !text.trim().isEmpty()) {
                        // 替换模板变量
                        text = replaceTemplateVariables(text, debtmanagement);

                        // 根据段落对齐方式添加适当的格式
                        ParagraphAlignment alignment = paragraph.getAlignment();
                        if (alignment == ParagraphAlignment.CENTER) {
                            // 居中对齐的段落（通常是标题）
                            emailContent.append(text).append("\n\n");
                        } else if (alignment == ParagraphAlignment.RIGHT) {
                            // 右对齐的段落（通常是落款）
                            emailContent.append("                                        ").append(text).append("\n");
                        } else {
                            // 左对齐或默认对齐的段落
                            // 检查是否需要首行缩进
                            if (paragraph.getIndentationFirstLine() > 0 || containsChinese(text)) {
                                emailContent.append("    ").append(text).append("\n");
                            } else {
                                emailContent.append(text).append("\n");
                            }
                        }

                        // 段落间添加空行
                        emailContent.append("\n");
                    }

                } else if (element instanceof XWPFTable) {
                    // 处理表格
                    XWPFTable table = (XWPFTable) element;
                    emailContent.append(convertTableToText(table, debtmanagement));
                    emailContent.append("\n");
                }
            }

            docxDocument.close();

            // 添加邮件页脚
            emailContent.append("---\n");
            emailContent.append("此邮件为系统自动发送，请勿直接回复。如有疑问，请联系相关业务人员。");

            return emailContent.toString();
        }
    }

    /**
     * 替换模板变量
     * @param text 原始文本
     * @param debtmanagement 债务管理数据
     * @return 替换后的文本
     */
    private String replaceTemplateVariables(String text, DfdwJysjDebtmanagement debtmanagement) {
        if (text == null || debtmanagement == null) {
            return text;
        }

        // 替换常用的模板变量
        text = text.replace("${companyName}", debtmanagement.getCompanyName() != null ? debtmanagement.getCompanyName() : "");
        text = text.replace("${projectName}", debtmanagement.getProjectName() != null ? debtmanagement.getProjectName() : "");
        text = text.replace("${groupName}", debtmanagement.getGroupName() != null ? debtmanagement.getGroupName() : "");
        text = text.replace("${currentDate}", formatDate(new Date(), "yyyy年MM月dd日"));

        // 可以根据需要添加更多模板变量
        if (debtmanagement.getReviewAmount() != null) {
            text = text.replace("${reviewAmount}", debtmanagement.getReviewAmount().toString());
        }

        return text;
    }

    /**
     * 将表格转换为文本格式
     * @param table XWPFTable对象
     * @param debtmanagement 债务管理数据
     * @return 表格的文本表示
     */
    private String convertTableToText(XWPFTable table, DfdwJysjDebtmanagement debtmanagement) {
        StringBuilder tableText = new StringBuilder();

        if (table.getRows().size() > 0) {
            for (XWPFTableRow row : table.getRows()) {
                StringBuilder rowText = new StringBuilder();
                for (XWPFTableCell cell : row.getTableCells()) {
                    String cellText = cell.getText();
                    if (cellText != null && !cellText.trim().isEmpty()) {
                        // 替换单元格中的模板变量
                        cellText = replaceTemplateVariables(cellText, debtmanagement);
                        rowText.append(cellText).append("\t");
                    } else {
                        rowText.append("\t");
                    }
                }
                // 移除最后一个制表符并添加换行
                if (rowText.length() > 0) {
                    tableText.append(rowText.toString().replaceAll("\t$", "")).append("\n");
                }
            }
        }

        return tableText.toString();
    }

    /**
     * 从文件ID列表中查找docx文件ID
     * @param fileIds 文件ID字符串，多个ID用逗号分隔
     * @return 第一个找到的docx文件ID，如果没有找到则返回null
     */
    private String findDocxFileId(String fileIds) {
        if (fileIds == null || fileIds.trim().isEmpty()) {
            return null;
        }

        try {
            // 分割文件ID
            String[] idArray = fileIds.split(",");

            for (String fileId : idArray) {
                fileId = fileId.trim();
                if (!fileId.isEmpty()) {
                    // 获取文件信息
                    TFile tFile = fileService.getById(fileId);
                    if (tFile != null && tFile.getFilename() != null &&
                        tFile.getFilename().toLowerCase().endsWith(".docx")) {
                        return fileId;
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("查找docx文件ID失败: " + e.getMessage());
            e.printStackTrace();
        }

        return null;
    }

    /**
     * 将输入流转换为PDF文件（默认按DOCX处理）
     */
    private File convertToPdf(InputStream inputStream, String originalFilename) {
        try {
            String tempDir = System.getProperty("java.io.tmpdir");
            String pdfFileName = getFileNameWithoutExtension(originalFilename) + ".pdf";
            File pdfFile = new File(tempDir, pdfFileName);

            // 如果原文件已经是PDF，直接保存
            if (originalFilename.toLowerCase().endsWith(".pdf")) {
                try (FileOutputStream fos = new FileOutputStream(pdfFile)) {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        fos.write(buffer, 0, bytesRead);
                    }
                }
                return pdfFile;
            }
            //判断是不是docx 如果不是就直接返回文件
            if (!originalFilename.toLowerCase().endsWith(".docx")) {
                return pdfFile;
            }
            // 默认都按DOCX格式处理
            return convertDocxToPdf(inputStream, pdfFile);

        } catch (Exception e) {
            System.err.println("转换PDF失败: " + originalFilename + ", " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }




    /**
     * 将DOCX文档转换为PDF
     */
    private File convertDocxToPdf(InputStream inputStream, File pdfFile) {
        try {
            // 使用Apache POI读取DOCX文档
            XWPFDocument docxDocument = new XWPFDocument(inputStream);

            // 创建PDF文档
            com.itextpdf.text.Document pdfDocument = new com.itextpdf.text.Document();
            PdfWriter.getInstance(pdfDocument, new FileOutputStream(pdfFile));
            pdfDocument.open();

            // 设置中文字体
            BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            com.itextpdf.text.Font normalFont = new com.itextpdf.text.Font(baseFont, 12);
            com.itextpdf.text.Font boldFont = new com.itextpdf.text.Font(baseFont, 12, com.itextpdf.text.Font.BOLD);

            // 按照文档中的顺序处理所有元素（段落和表格）
            List<IBodyElement> bodyElements = docxDocument.getBodyElements();

            for (IBodyElement element : bodyElements) {
                if (element instanceof XWPFParagraph) {
                    // 处理段落
                    XWPFParagraph paragraph = (XWPFParagraph) element;
                    String text = paragraph.getText();

                    if (text != null && !text.trim().isEmpty()) {
                        // 检查段落样式
                        boolean isBold = false;
                        List<XWPFRun> runs = paragraph.getRuns();
                        if (!runs.isEmpty() && runs.get(0).isBold()) {
                            isBold = true;
                        }

                        // 添加段落到PDF
                        Paragraph pdfParagraph = new Paragraph(text, isBold ? boldFont : normalFont);

                        // 设置段落对齐方式
                        ParagraphAlignment alignment = paragraph.getAlignment();
                        if (alignment == ParagraphAlignment.CENTER) {
                            pdfParagraph.setAlignment(com.itextpdf.text.Element.ALIGN_CENTER);
                        } else if (alignment == ParagraphAlignment.RIGHT) {
                            pdfParagraph.setAlignment(com.itextpdf.text.Element.ALIGN_RIGHT);
                        } else {
                            pdfParagraph.setAlignment(com.itextpdf.text.Element.ALIGN_LEFT);
                        }

                        // 处理首行缩进
                        if (paragraph.getIndentationFirstLine() > 0) {
                            // DOCX中的缩进单位是twips (1/20 point)，转换为points
                            float firstLineIndent = paragraph.getIndentationFirstLine() / 20f;
                            pdfParagraph.setFirstLineIndent(firstLineIndent);
                        } else {
                            // 如果没有明确的首行缩进，检查是否有左缩进
                            if (paragraph.getIndentationLeft() > 0) {
                                float leftIndent = paragraph.getIndentationLeft() / 20f;
                                pdfParagraph.setIndentationLeft(leftIndent);
                            } else {
                                // 默认中文段落首行缩进2个字符（约28points）
                                if (containsChinese(text)) {
                                    pdfParagraph.setFirstLineIndent(28f);
                                }
                            }
                        }

                        // 处理右缩进
                        if (paragraph.getIndentationRight() > 0) {
                            float rightIndent = paragraph.getIndentationRight() / 20f;
                            pdfParagraph.setIndentationRight(rightIndent);
                        }

                        // 处理行间距
                        if (paragraph.getSpacingAfter() > 0) {
                            float spacingAfter = paragraph.getSpacingAfter() / 20f;
                            pdfParagraph.setSpacingAfter(spacingAfter);
                        }

                        if (paragraph.getSpacingBefore() > 0) {
                            float spacingBefore = paragraph.getSpacingBefore() / 20f;
                            pdfParagraph.setSpacingBefore(spacingBefore);
                        }

                        pdfDocument.add(pdfParagraph);

                        // 只在段落间距不足时添加空行
                        if (paragraph.getSpacingAfter() <= 0) {
                            pdfDocument.add(new Paragraph(" ")); // 添加空行
                        }
                    }

                } else if (element instanceof XWPFTable) {
                    // 处理表格
                    XWPFTable table = (XWPFTable) element;

                    if (table.getRows().size() > 0) {
                        // 创建PDF表格
                        int columnCount = table.getRow(0).getTableCells().size();
                        com.itextpdf.text.pdf.PdfPTable pdfTable = new com.itextpdf.text.pdf.PdfPTable(columnCount);
                        pdfTable.setWidthPercentage(100);
                        pdfTable.setSpacingBefore(10f);
                        pdfTable.setSpacingAfter(10f);

                        // 添加表格行
                        for (XWPFTableRow row : table.getRows()) {
                            for (XWPFTableCell cell : row.getTableCells()) {
                                String cellText = cell.getText();
                                if (cellText == null) cellText = "";

                                // 检查单元格中是否有粗体文字
                                boolean isCellBold = false;
                                for (XWPFParagraph cellPara : cell.getParagraphs()) {
                                    for (XWPFRun run : cellPara.getRuns()) {
                                        if (run.isBold()) {
                                            isCellBold = true;
                                            break;
                                        }
                                    }
                                    if (isCellBold) break;
                                }

                                com.itextpdf.text.pdf.PdfPCell pdfCell = new com.itextpdf.text.pdf.PdfPCell(
                                    new Paragraph(cellText, isCellBold ? boldFont : normalFont));
                                pdfCell.setHorizontalAlignment(com.itextpdf.text.Element.ALIGN_CENTER);
                                pdfCell.setVerticalAlignment(com.itextpdf.text.Element.ALIGN_MIDDLE);
                                pdfCell.setPadding(5f);
                                pdfTable.addCell(pdfCell);
                            }
                        }

                        pdfDocument.add(pdfTable);
                        pdfDocument.add(new Paragraph(" ")); // 表格后添加空行
                    }
                }
            }

            pdfDocument.close();
            docxDocument.close();

            System.out.println("DOCX转PDF成功");
            return pdfFile;

        } catch (Exception e) {
            System.err.println("DOCX转PDF失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 将XLSX文档转换为PDF
     */
    private File convertXlsxToPdf(InputStream inputStream, File pdfFile) {
        try {
            // 使用Apache POI读取XLSX文档
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);

            // 创建PDF文档
            com.itextpdf.text.Document pdfDocument = new com.itextpdf.text.Document();
            PdfWriter.getInstance(pdfDocument, new FileOutputStream(pdfFile));
            pdfDocument.open();

            // 设置中文字体
            BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            com.itextpdf.text.Font normalFont = new com.itextpdf.text.Font(baseFont, 10);
            com.itextpdf.text.Font titleFont = new com.itextpdf.text.Font(baseFont, 14, com.itextpdf.text.Font.BOLD);

            // 遍历所有工作表
            for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
                XSSFSheet sheet = workbook.getSheetAt(sheetIndex);
                String sheetName = sheet.getSheetName();

                // 添加工作表标题
                pdfDocument.add(new Paragraph("工作表: " + sheetName, titleFont));
                pdfDocument.add(new Paragraph(" "));

                // 获取有数据的行数和列数
                int lastRowNum = sheet.getLastRowNum();
                if (lastRowNum >= 0) {
                    Row firstRow = sheet.getRow(0);
                    int maxCols = firstRow != null ? firstRow.getLastCellNum() : 0;

                    if (maxCols > 0) {
                        // 创建PDF表格
                        com.itextpdf.text.pdf.PdfPTable pdfTable = new com.itextpdf.text.pdf.PdfPTable(maxCols);
                        pdfTable.setWidthPercentage(100);

                        // 添加数据行
                        for (int rowIndex = 0; rowIndex <= lastRowNum; rowIndex++) {
                            Row row = sheet.getRow(rowIndex);
                            if (row != null) {
                                for (int colIndex = 0; colIndex < maxCols; colIndex++) {
                                    Cell cell = row.getCell(colIndex);
                                    String cellValue = "";

                                    if (cell != null) {
                                        switch (cell.getCellType()) {
                                            case STRING:
                                                cellValue = cell.getStringCellValue();
                                                break;
                                            case NUMERIC:
                                                if (DateUtil.isCellDateFormatted(cell)) {
                                                    cellValue = new SimpleDateFormat("yyyy-MM-dd").format(cell.getDateCellValue());
                                                } else {
                                                    cellValue = String.valueOf(cell.getNumericCellValue());
                                                }
                                                break;
                                            case BOOLEAN:
                                                cellValue = String.valueOf(cell.getBooleanCellValue());
                                                break;
                                            case FORMULA:
                                                cellValue = cell.getCellFormula();
                                                break;
                                            default:
                                                cellValue = "";
                                        }
                                    }

                                    com.itextpdf.text.pdf.PdfPCell pdfCell = new com.itextpdf.text.pdf.PdfPCell(new Paragraph(cellValue, normalFont));
                                    pdfCell.setHorizontalAlignment(com.itextpdf.text.Element.ALIGN_CENTER);
                                    pdfTable.addCell(pdfCell);
                                }
                            } else {
                                // 空行处理
                                for (int colIndex = 0; colIndex < maxCols; colIndex++) {
                                    pdfTable.addCell(new com.itextpdf.text.pdf.PdfPCell(new Paragraph("", normalFont)));
                                }
                            }
                        }

                        pdfDocument.add(pdfTable);
                    }
                }

                pdfDocument.add(new Paragraph(" ")); // 工作表间隔
            }

            pdfDocument.close();
            workbook.close();

            System.out.println("XLSX转PDF成功");
            return pdfFile;

        } catch (Exception e) {
            System.err.println("XLSX转PDF失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
   





    /**
     * 创建ZIP压缩文件
     */
    private File createZipFile(List<File> files, String zipFileName) {
        try {
            String tempDir = System.getProperty("java.io.tmpdir");
            File zipFile = new File(tempDir, zipFileName);

            try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {
                for (File file : files) {
                    try (FileInputStream fis = new FileInputStream(file)) {
                        ZipEntry zipEntry = new ZipEntry(file.getName());
                        zos.putNextEntry(zipEntry);

                        byte[] buffer = new byte[1024];
                        int bytesRead;
                        while ((bytesRead = fis.read(buffer)) != -1) {
                            zos.write(buffer, 0, bytesRead);
                        }
                        zos.closeEntry();
                    }
                }
            }

            return zipFile;
        } catch (Exception e) {
            System.err.println("创建ZIP文件失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 发送带附件的邮件
     */
    private void sendMailWithAttachments(JavaMailSenderImpl mailSender, String to, String subject,
                                       String content, List<File> attachments) throws Exception {
        MimeMessage mimeMessage = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");

        // 确保发件人地址不为空
        String fromAddress = mailSender.getUsername();
        if (fromAddress == null || fromAddress.trim().isEmpty()) {
            throw new RuntimeException("邮件发送失败：发件人地址未配置");
        }

        helper.setFrom(fromAddress);
        helper.setTo(to);
        helper.setSubject(subject);
        helper.setText(content);

        // 添加附件
        for (File attachment : attachments) {
            helper.addAttachment(attachment.getName(), attachment);
        }

        mailSender.send(mimeMessage);

        // 清理临时文件
        for (File attachment : attachments) {
            attachment.delete();
        }
    }

    /**
     * 获取不带扩展名的文件名
     */
    private String getFileNameWithoutExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "file";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return filename.substring(0, lastDotIndex);
        }
        return filename;
    }

    /**
     * 检查文本是否包含中文字符
     */
    private boolean containsChinese(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        for (char c : text.toCharArray()) {
            if (c >= 0x4E00 && c <= 0x9FFF) {
                return true;
            }
        }
        return false;
    }

    /**
     * 生成催办函docx文档
     */
    @Override
    public MultipartFile reminderLetter(DfdwJysjCollection collection) throws IOException {

        // 获取关联的业务数据
        DfdwJysjBusinessData businessData = businessDataService.getById(collection.getBusinessDataId());
        if (businessData == null) {
            throw new RuntimeException("关联的业务数据不存在");
        }

        // 创建Word文档
        XWPFDocument document = new XWPFDocument();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try {
            // 标题
            XWPFParagraph titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = titleParagraph.createRun();
            titleRun.setText("工程结算催办函");
            titleRun.setBold(true);
            titleRun.setFontSize(22);
            titleRun.setFontFamily("宋体");

            // 空行
            document.createParagraph();

            // 正文第一段
            XWPFParagraph paragraph1 = document.createParagraph();
            XWPFRun run1 = paragraph1.createRun();
            run1.setText(businessData.getCompanyName() + "：");
            run1.setFontFamily("宋体");
            run1.setFontSize(14);

            // 正文第二段
            XWPFParagraph paragraph2 = document.createParagraph();
            XWPFRun run2 = paragraph2.createRun();
            String content = "    因" + businessData.getProjectName() + "项目，" +
                    "本公司于" + formatDate(businessData.getTransferTime(), "YYYY年MM月DD日") +
                    "与贵公司/单位签订合同编号为" + businessData.getProjectCode() + "的《" +
                    businessData.getProjectName() + "施工合同》。" +
                    "合同签订后，本公司按约完成相关工作，于" +
                    formatDate(businessData.getTransferTime(), "YYYY年MM月DD日") +
                    "竣工并交付。";
            run2.setText(content);
            run2.setFontFamily("宋体");
            run2.setFontSize(14);

            // 正文第三段
            XWPFParagraph paragraph3 = document.createParagraph();
            XWPFRun run3 = paragraph3.createRun();
            String content2 = "    截止本函发送之日，贵公司/单位仍然未完成结算审计，" +
                    "导致工程款项的结算支付无法完成。故，向贵公司/单位发送本函，" +
                    "要求于" + formatDate(collection.getCollectionDate(), "YYYY年MM月DD日") +
                    "之前完成结算审计工作。如存在客观困难，则请于" +
                    formatDate(collection.getCollectionDate(), "YYYY年MM月DD日") +
                    "之前联系本公司，协商解决方案。";
            run3.setText(content2);
            run3.setFontFamily("宋体");
            run3.setFontSize(14);

            // 空行
            document.createParagraph();

            // 落款
            XWPFParagraph signatureParagraph1 = document.createParagraph();
            signatureParagraph1.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun signatureRun1 = signatureParagraph1.createRun();
            signatureRun1.setText("催告单位："+businessData.getCustomer());
            signatureRun1.setFontFamily("宋体");
            signatureRun1.setFontSize(14);

            XWPFParagraph signatureParagraph2 = document.createParagraph();
            signatureParagraph2.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun signatureRun2 = signatureParagraph2.createRun();
            signatureRun2.setText("催告时间：" + formatDate(new Date(), "YYYY年MM月DD日"));
            signatureRun2.setFontFamily("宋体");
            signatureRun2.setFontSize(14);

            // 添加多个空行
            for (int i = 0; i < 19; i++) {
                document.createParagraph();
            }

            // 操作提示
            XWPFParagraph tipsParagraph = document.createParagraph();
            XWPFRun tipsRun = tipsParagraph.createRun();
            tipsRun.setText("操作提示：");
            tipsRun.setBold(true);
            tipsRun.setFontFamily("宋体");
            tipsRun.setFontSize(14);

            // 提示内容
            String[] tips = {
                    "1、请用EMS发送，填写快递面单时，注明项目名称和函件名称，如写不下，可以简写。----说明寄送的文件是什么。",
                    "2、EMS寄送后，留好快递面单，及时根据快递单号查询送达信息，并将送达信息截屏保存。----说明已经送达。",
                    "3、寄送EMS时，函件应同时留下备份的复印件，并将寄送的原件和复印备份件一起盖骑缝章。----说明原件已经寄出。",
                    "4、寄送时，填写的收件地址，应是合同约定的送达地址，如果没有约定送达地址，则选择官方网站或工商信息登记的地址。",
                    "5、寄送时，填写的收件人，应是合同约定的工作联系人，官方网站或工商信息登记的法定代表人（负责人）。"
            };

            for (String tip : tips) {
                XWPFParagraph tipParagraph = document.createParagraph();
                XWPFRun tipRun = tipParagraph.createRun();
                tipRun.setText(tip);
                tipRun.setFontFamily("宋体");
                tipRun.setFontSize(14);
            }

            // 将文档写入ByteArrayOutputStream
            document.write(outputStream);
//            document.write(response.getOutputStream());
            // 生成文件名
            String fileName = "工程结算催办函_" + businessData.getProjectName() + "_" +
                    new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".docx";

            // 转换为MultipartFile
            byte[] fileBytes = outputStream.toByteArray();

            return new MockMultipartFile(
                    "file",
                    fileName,
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    fileBytes
            );

        } finally {
            document.close();
            outputStream.close();
        }
    }


    @Override
    public MultipartFile auditRequestLetter(DfdwJysjCollection collection) throws IOException {

        // 获取关联的业务数据
        DfdwJysjBusinessData businessData = businessDataService.getById(collection.getBusinessDataId());
        if (businessData == null) {
            throw new RuntimeException("关联的业务数据不存在");
        }

        // 创建Word文档
        XWPFDocument document = new XWPFDocument();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try {
            // 标题
            XWPFParagraph titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = titleParagraph.createRun();
            titleRun.setText("工程审计请办函");
            titleRun.setBold(true);
            titleRun.setFontSize(22);
            titleRun.setFontFamily("宋体");

            // 空行
            document.createParagraph();

            // 正文第一段
            XWPFParagraph paragraph1 = document.createParagraph();
            XWPFRun run1 = paragraph1.createRun();
            run1.setText(businessData.getCompanyName() + "政府部门或单位：");
            run1.setFontFamily("宋体");
            run1.setFontSize(14);

            // 正文第二段
            XWPFParagraph paragraph2 = document.createParagraph();
            XWPFRun run2 = paragraph2.createRun();
            String content = "    因" + businessData.getProjectName() + "项目，" +
                    "本公司于" + formatDate(businessData.getTransferTime(), "YYYY年MM月DD日") +
                    "与贵单位签订《" + businessData.getProjectName() + "施工、设计或设备合同》。" +
                    "合同签订后，本公司按约完成相关工作，于" +
                    formatDate(businessData.getTransferTime(), "YYYY年MM月DD日") +
                    "完成并交付。";
            run2.setText(content);
            run2.setFontFamily("宋体");
            run2.setFontSize(14);

            // 正文第三段
            XWPFParagraph paragraph3 = document.createParagraph();
            XWPFRun run3 = paragraph3.createRun();
            String content2 = "    截止本函发送之日，贵单位委托的第三方审计机构仍然未出具审计意见，" +
                    "导致工程款项的结算支付无法完成。故，向贵单位发送本函，" +
                    "请于" + formatDate(collection.getCollectionDate(), "YYYY年MM月DD日") +
                    "之前完成结算审计工作。如存在客观困难，则请于" +
                    formatDate(collection.getCollectionDate(), "YYYY年MM月DD日") +
                    "之前联系本公司，协商解决方案。";
            run3.setText(content2);
            run3.setFontFamily("宋体");
            run3.setFontSize(14);

            // 空行
            document.createParagraph();

            // 落款
            XWPFParagraph signatureParagraph1 = document.createParagraph();
            signatureParagraph1.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun signatureRun1 = signatureParagraph1.createRun();
            signatureRun1.setText("请办单位："+businessData.getCustomer());
            signatureRun1.setFontFamily("宋体");
            signatureRun1.setFontSize(14);

            XWPFParagraph signatureParagraph2 = document.createParagraph();
            signatureParagraph2.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun signatureRun2 = signatureParagraph2.createRun();
            signatureRun2.setText(formatDate(new Date(), "YYYY年MM月DD日"));
            signatureRun2.setFontFamily("宋体");
            signatureRun2.setFontSize(14);

            // 添加多个空行
            for (int i = 0; i < 8; i++) {
                document.createParagraph();
            }

            // 操作提示
            XWPFParagraph tipsParagraph = document.createParagraph();
            XWPFRun tipsRun = tipsParagraph.createRun();
            tipsRun.setText("操作提示：");
            tipsRun.setBold(true);
            tipsRun.setFontFamily("宋体");
            tipsRun.setFontSize(14);

            // 提示内容
            String[] tips = {
                "1、请用EMS发送，填写快递面单时，注明项目名称和函件名称，如写不下，可以简写。----说明寄送的文件是什么。",
                "2、EMS寄送后，留好快递面单，及时根据快递单号查询送达信息，并将送达信息截屏保存。----说明已经送达。",
                "3、寄送EMS时，函件应同时留下备份的复印件，并将寄送的原件和复印备份件一起盖骑缝章。----说明原件已经寄出。",
                "4、寄送时，填写的收件地址，应是合同约定的送达地址，如果没有约定送达地址，则选择官方网站或工商信息登记的地址。",
                "5、寄送时，填写的收件人，应是合同约定的工作联系人，官方网站或工商信息登记的法定代表人（负责人）。"
            };

            for (String tip : tips) {
                XWPFParagraph tipParagraph = document.createParagraph();
                XWPFRun tipRun = tipParagraph.createRun();
                tipRun.setText(tip);
                tipRun.setFontFamily("宋体");
                tipRun.setFontSize(14);
            }

            // 将文档写入ByteArrayOutputStream
            document.write(outputStream);

            // 生成文件名
            String fileName = "工程审计请办函_" + businessData.getProjectName() + "_" +
                    new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".docx";

            // 转换为MultipartFile
            byte[] fileBytes = outputStream.toByteArray();

            return new MockMultipartFile(
                "file",
                fileName,
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                fileBytes
            );

        } finally {
            document.close();
            outputStream.close();
        }
    }

    @Override
    public MultipartFile paymentDemandLetter(DfdwJysjCollection collection) throws IOException {


        // 获取关联的业务数据
        DfdwJysjBusinessData businessData = businessDataService.getById(collection.getBusinessDataId());
        if (businessData == null) {
            throw new RuntimeException("关联的业务数据不存在");
        }

        // 创建Word文档
        XWPFDocument document = new XWPFDocument();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try {
            // 标题
            XWPFParagraph titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = titleParagraph.createRun();
            titleRun.setText("催 款 函");
            titleRun.setBold(true);
            titleRun.setFontSize(16);
            titleRun.setFontFamily("宋体");

            // 空行
            document.createParagraph();

            // 收件人
            XWPFParagraph paragraph1 = document.createParagraph();
            XWPFRun run1 = paragraph1.createRun();
            run1.setText(businessData.getCompanyName() + "：");
            run1.setFontFamily("宋体");
            run1.setFontSize(12);

            // 正文第一段
            XWPFParagraph paragraph2 = document.createParagraph();
            XWPFRun run2 = paragraph2.createRun();
            String content1 = "    " + formatDate(businessData.getTransferTime(), "YYYY年MM月DD日") +
                    "，贵方与我司签订编号为" + businessData.getProjectCode() + "的《" +
                    businessData.getProjectName() + "》一份，约定贵方于" +
                    formatDate(businessData.getTransferTime(), "YYYY年MM月DD日") +
                    "前向我司支付合同款项共计" + businessData.getMoney() + "元。" +
                    "合同签订后，我司已经按约履行了合同项下义务。";
            run2.setText(content1);
            run2.setFontFamily("宋体");
            run2.setFontSize(12);

            // 正文第二段
            XWPFParagraph paragraph3 = document.createParagraph();
            XWPFRun run3 = paragraph3.createRun();
            String content2 = "    截至" + formatDate(new Date(), "YYYY年MM月DD日") +
                    "，贵方尚欠合同余款" + businessData.getMoney() + "元未按合同约定支付我司。现我司特致函贵方：";
            run3.setText(content2);
            run3.setFontFamily("宋体");
            run3.setFontSize(12);

            // 要求条款1
            XWPFParagraph paragraph4 = document.createParagraph();
            XWPFRun run4 = paragraph4.createRun();
            run4.setText("1.请贵方在收到本函后30日内将" + businessData.getMoney() + "元合同余款支付到我司账号；");
            run4.setFontFamily("宋体");
            run4.setFontSize(12);

            // 要求条款2
            XWPFParagraph paragraph5 = document.createParagraph();
            XWPFRun run5 = paragraph5.createRun();
            run5.setText("2.若贵方逾期未付，我司有理由认为贵方恶意拖欠，我司将循法律途径维护自身合法权益，且按照合同约定，贵方将承担相应的违约责任。");
            run5.setFontFamily("宋体");
            run5.setFontSize(12);

            // 结语
            XWPFParagraph paragraph6 = document.createParagraph();
            XWPFRun run6 = paragraph6.createRun();
            run6.setText("望贵方珍惜商誉，及时安排付款。");
            run6.setFontFamily("宋体");
            run6.setFontSize(12);

            XWPFParagraph paragraph7 = document.createParagraph();
            XWPFRun run7 = paragraph7.createRun();
            run7.setText("顺祝商祺！");
            run7.setFontFamily("宋体");
            run7.setFontSize(12);

            // 空行
            document.createParagraph();

            // 账户信息
            XWPFParagraph accountParagraph1 = document.createParagraph();
            XWPFRun accountRun1 = accountParagraph1.createRun();
            accountRun1.setText("我公司帐户名称: ");
            accountRun1.setFontFamily("宋体");
            accountRun1.setFontSize(12);

            XWPFParagraph accountParagraph2 = document.createParagraph();
            XWPFRun accountRun2 = accountParagraph2.createRun();
            accountRun2.setText("开  户  银  行: ");
            accountRun2.setFontFamily("宋体");
            accountRun2.setFontSize(12);

            XWPFParagraph accountParagraph3 = document.createParagraph();
            XWPFRun accountRun3 = accountParagraph3.createRun();
            accountRun3.setText("账          号: ");
            accountRun3.setFontFamily("宋体");
            accountRun3.setFontSize(12);

            // 空行
            document.createParagraph();

            // 落款
            XWPFParagraph signatureParagraph1 = document.createParagraph();
            signatureParagraph1.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun signatureRun1 = signatureParagraph1.createRun();
            signatureRun1.setText(businessData.getCustomer());
            signatureRun1.setFontFamily("宋体");
            signatureRun1.setFontSize(12);

            XWPFParagraph signatureParagraph2 = document.createParagraph();
            signatureParagraph2.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun signatureRun2 = signatureParagraph2.createRun();
            signatureRun2.setText("（盖章）");
            signatureRun2.setFontFamily("宋体");
            signatureRun2.setFontSize(12);

            XWPFParagraph signatureParagraph3 = document.createParagraph();
            signatureParagraph3.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun signatureRun3 = signatureParagraph3.createRun();
            signatureRun3.setText(formatDate(new Date(), "YYYY年MM月DD日"));
            signatureRun3.setFontFamily("宋体");
            signatureRun3.setFontSize(12);

            // 联系人信息
            XWPFParagraph contactParagraph1 = document.createParagraph();
            XWPFRun contactRun1 = contactParagraph1.createRun();
            contactRun1.setText("联系人：");
            contactRun1.setFontFamily("宋体");
            contactRun1.setFontSize(12);

            XWPFParagraph contactParagraph2 = document.createParagraph();
            XWPFRun contactRun2 = contactParagraph2.createRun();
            contactRun2.setText("【姓名】               【职位】");
            contactRun2.setFontFamily("宋体");
            contactRun2.setFontSize(12);

            XWPFParagraph contactParagraph3 = document.createParagraph();
            XWPFRun contactRun3 = contactParagraph3.createRun();
            contactRun3.setText("【联系方式】");
            contactRun3.setFontFamily("宋体");
            contactRun3.setFontSize(12);

            // 将文档写入ByteArrayOutputStream
            document.write(outputStream);

            // 生成文件名
            String fileName = "催款函_" + businessData.getProjectName() + "_" +
                    new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".docx";

            // 转换为MultipartFile
            byte[] fileBytes = outputStream.toByteArray();

            return new MockMultipartFile(
                "file",
                fileName,
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                fileBytes
            );

        } finally {
            document.close();
            outputStream.close();
        }
    }

    @Override
    public MultipartFile dunningLetter(DfdwJysjCollection collection) throws IOException {

        // 获取关联的业务数据
        DfdwJysjBusinessData businessData = businessDataService.getById(collection.getBusinessDataId());
        if (businessData == null) {
            throw new RuntimeException("关联的业务数据不存在");
        }

        // 创建Word文档
        XWPFDocument document = new XWPFDocument();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try {
            // 标题
            XWPFParagraph titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = titleParagraph.createRun();
            titleRun.setText("催 告 函");
            titleRun.setBold(true);
            titleRun.setFontSize(15); // 小三号字体
            titleRun.setFontFamily("宋体");

            // 空行
            document.createParagraph();

            // 编号
            XWPFParagraph numberParagraph = document.createParagraph();
            numberParagraph.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun numberRun = numberParagraph.createRun();
            numberRun.setText("编号：          ");
            numberRun.setFontFamily("宋体");
            numberRun.setFontSize(9); // 小五号字体

            // 往来单位名称
            XWPFParagraph companyParagraph = document.createParagraph();
            XWPFRun companyRun = companyParagraph.createRun();
            companyRun.setText("往来单位名称：");
            companyRun.setFontFamily("宋体");
            companyRun.setFontSize(9);

            // 正文第一段
            XWPFParagraph paragraph1 = document.createParagraph();
            XWPFRun run1 = paragraph1.createRun();
            run1.setText("    根据我公司财务管理要求，需定期与各户核对账款收付情况，并进行相应催收，请贵单位配合我们工作。");
            run1.setFontFamily("宋体");
            run1.setFontSize(9);

            // 空行
            document.createParagraph();

            // 第一条
            XWPFParagraph paragraph2 = document.createParagraph();
            XWPFRun run2 = paragraph2.createRun();
            run2.setText("    1、经我公司历年来核对催收，根据贵单位已付款情况，目前我公司财务查核后的欠款情况如下：");
            run2.setFontFamily("宋体");
            run2.setFontSize(9);

            // 空行
            document.createParagraph();

            // 金额单位
            XWPFParagraph unitParagraph = document.createParagraph();
            unitParagraph.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun unitRun = unitParagraph.createRun();
            unitRun.setText("金额单位：元");
            unitRun.setFontFamily("宋体");
            unitRun.setFontSize(9);

            // 创建表格
            XWPFTable table = document.createTable(3, 5);
            table.setWidth("100%");

            // 设置表头
            XWPFTableRow headerRow = table.getRow(0);
            setCellText(headerRow.getCell(0), "截止日期", 9);
            setCellText(headerRow.getCell(1), "工程名称及编号", 9);
            setCellText(headerRow.getCell(2), "结算价格", 9);
            setCellText(headerRow.getCell(3), "已付款金额", 9);
            setCellText(headerRow.getCell(4), "备    注", 9);

            // 设置数据行（空行供填写）
            XWPFTableRow dataRow1 = table.getRow(1);
            for (int i = 0; i < 5; i++) {
                setCellText(dataRow1.getCell(i), "", 9);
            }

            XWPFTableRow dataRow2 = table.getRow(2);
            for (int i = 0; i < 5; i++) {
                setCellText(dataRow2.getCell(i), "", 9);
            }

            // 空行
            document.createParagraph();

            // 第二条
            XWPFParagraph paragraph3 = document.createParagraph();
            XWPFRun run3 = paragraph3.createRun();
            run3.setText("    2、其他事项。");
            run3.setFontFamily("宋体");
            run3.setFontSize(9);

            // 第一个如单位段落
            XWPFParagraph paragraph4 = document.createParagraph();
            XWPFRun run4 = paragraph4.createRun();
            run4.setText("    如贵单位对根据结算价格和已付款金额没有异议，则请贵单位尽快安排付款补足欠付款项。");
            run4.setFontFamily("宋体");
            run4.setFontSize(9);

            // 第二个如单位段落
            XWPFParagraph paragraph5 = document.createParagraph();
            XWPFRun run5 = paragraph5.createRun();
            run5.setText("    如贵单位对款项金额存在异议，请在收到本函后七个工作日内向我公司提出异议的具体理由，由双方协商处理。");
            run5.setFontFamily("宋体");
            run5.setFontSize(9);

            // 此致敬礼
            XWPFParagraph paragraph6 = document.createParagraph();
            XWPFRun run6 = paragraph6.createRun();
            run6.setText("    特此催告！");
            run6.setFontFamily("宋体");
            run6.setFontSize(9);

            // 空行
            document.createParagraph();
            document.createParagraph();

            // 催告单位和日期
            XWPFParagraph signatureParagraph1 = document.createParagraph();
            signatureParagraph1.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun signatureRun1 = signatureParagraph1.createRun();
            signatureRun1.setText("                                                催告单位：");
            signatureRun1.setFontFamily("宋体");
            signatureRun1.setFontSize(9);

            XWPFParagraph signatureParagraph2 = document.createParagraph();
            signatureParagraph2.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun signatureRun2 = signatureParagraph2.createRun();
            signatureRun2.setText("                                              年   月   日");
            signatureRun2.setFontFamily("宋体");
            signatureRun2.setFontSize(9);

            // 空行
            document.createParagraph();

            // 创建签收表格
            XWPFTable signTable = document.createTable(2, 2);
            signTable.setWidth("100%");

            // 第一行
            XWPFTableRow signRow1 = signTable.getRow(0);
            setCellText(signRow1.getCell(0), "签收单位", 9);
            setCellText(signRow1.getCell(1), "签收单位", 9);

            XWPFTableRow signRow2 = signTable.getRow(1);
            setCellText(signRow2.getCell(0), "对前述金额无异议。", 9);
            setCellText(signRow2.getCell(1), "对前述金额有异议。", 9);

            // 在每个单元格中添加签名区域
            addSignatureArea(signRow2.getCell(0));
            addSignatureArea(signRow2.getCell(1));

            // 将文档写入ByteArrayOutputStream
            document.write(outputStream);

            // 生成文件名
            String fileName = "催告函_" + businessData.getProjectName() + "_" +
                    new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".docx";

            // 转换为MultipartFile
            byte[] fileBytes = outputStream.toByteArray();

            return new MockMultipartFile(
                "file",
                fileName,
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                fileBytes
            );

        } finally {
            document.close();
            outputStream.close();
        }
    }

    /**
     * 设置表格单元格文本
     */
    private void setCellText(XWPFTableCell cell, String text, int fontSize) {
        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        run.setFontFamily("宋体");
        run.setFontSize(fontSize);
        paragraph.setAlignment(ParagraphAlignment.CENTER);
    }

    /**
     * 添加签名区域
     */
    private void addSignatureArea(XWPFTableCell cell) {
        // 添加空行用于签名
        for (int i = 0; i < 3; i++) {
            XWPFParagraph p = cell.addParagraph();
            XWPFRun r = p.createRun();
            r.setText("");
            r.setFontSize(9);
        }

        // 添加盖章和日期
        XWPFParagraph sealParagraph = cell.addParagraph();
        sealParagraph.setAlignment(ParagraphAlignment.RIGHT);
        XWPFRun sealRun = sealParagraph.createRun();
        sealRun.setText("盖章：");
        sealRun.setFontFamily("宋体");
        sealRun.setFontSize(9);

        XWPFParagraph dateParagraph = cell.addParagraph();
        dateParagraph.setAlignment(ParagraphAlignment.RIGHT);
        XWPFRun dateRun = dateParagraph.createRun();
        dateRun.setText("年   月   日");
        dateRun.setFontFamily("宋体");
        dateRun.setFontSize(9);
    }

    

    /**
     * 格式化日期
     *
     * @param date 日期
     * @param pattern 格式模式
     * @return 格式化后的日期字符串
     */
    private String formatDate(Date date, String pattern) {
        if (date == null) {
            return "XXXX年XX月XX日";
        }

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
            return sdf.format(date);
        } catch (Exception e) {
            return "XXXX年XX月XX日";
        }
    }






























    /**
     * 获取催收期数的阿拉伯数字形式
     * 用于计算或比较操作
     *
     * @param chinesePeriods 中文数字形式的催收期数
     * @return 阿拉伯数字形式的催收期数，如果转换失败返回0
     */
    public int getPeriodNumber(String chinesePeriods) {
        if (chinesePeriods == null || chinesePeriods.trim().isEmpty()) {
            return 0;
        }

        String periods = chinesePeriods.trim();

        // 如果已经是阿拉伯数字
        if (periods.matches("\\d+")) {
            try {
                return Integer.parseInt(periods);
            } catch (NumberFormatException e) {
                return 0;
            }
        }

        // 如果是中文数字，转换为阿拉伯数字
        try {
            return convertChineseToNumber(periods);
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 从"第xxx期"格式的字符串中提取中间的xxx部分
     *
     * @param periodText 期数文本，格式如"第一期"、"第10期"、"第二十三期"等
     * @return 提取出的中间部分，如"一"、"10"、"二十三"等
     *
     * 示例：
     * "第一期" -> "一"
     * "第10期" -> "10"
     * "第二十三期" -> "二十三"
     */
    public String extractPeriodMiddle(String periodText) {
        if (periodText == null || periodText.trim().isEmpty()) {
            return "";
        }

        String text = periodText.trim();

        // 检查是否符合"第xxx期"的格式
        if (text.startsWith("第") && text.endsWith("期") && text.length() > 2) {
            // 提取中间部分（去掉"第"和"期"）
            return text.substring(1, text.length() - 1);
        }

        // 如果不符合格式，返回原文本
        return text;
    }

    /**
     * 从"第xxx期"格式的字符串中提取中间的数字部分
     * 支持中文数字和阿拉伯数字
     *
     * @param periodText 期数文本，格式如"第一期"、"第10期"、"第二十三期"等
     * @return 提取出的数字部分，如果是中文数字会转换为阿拉伯数字
     *
     * 示例：
     * "第一期" -> 1
     * "第10期" -> 10
     * "第二十三期" -> 23
     * "第一百零一期" -> 101
     */
    public int extractPeriodNumber(String periodText) {
        if (periodText == null || periodText.trim().isEmpty()) {
            throw new IllegalArgumentException("期数文本不能为空");
        }

        String text = periodText.trim();

        // 检查是否符合"第xxx期"的格式
        if (!text.startsWith("第") || !text.endsWith("期")) {
            throw new IllegalArgumentException("期数文本格式不正确，应为'第xxx期'的格式");
        }

        // 提取中间部分（去掉"第"和"期"）
        String middlePart = text.substring(1, text.length() - 1);

        if (middlePart.isEmpty()) {
            throw new IllegalArgumentException("期数文本中缺少数字部分");
        }

        // 判断是阿拉伯数字还是中文数字
        if (middlePart.matches("\\d+")) {
            // 阿拉伯数字
            try {
                return Integer.parseInt(middlePart);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("无法解析期数中的阿拉伯数字: " + middlePart);
            }
        } else {
            // 中文数字
            try {
                return convertChineseToNumber(middlePart);
            } catch (Exception e) {
                throw new IllegalArgumentException("无法解析期数中的中文数字: " + middlePart + ", 错误: " + e.getMessage());
            }
        }
    }

    /**
     * 从"第xxx期"格式的字符串中提取中间的数字部分（字符串形式）
     * 保持原始格式不变
     *
     * @param periodText 期数文本，格式如"第一期"、"第10期"等
     * @return 提取出的数字部分的字符串形式
     *
     * 示例：
     * "第一期" -> "一"
     * "第10期" -> "10"
     * "第二十三期" -> "二十三"
     */
    public String extractPeriodString(String periodText) {
        if (periodText == null || periodText.trim().isEmpty()) {
            throw new IllegalArgumentException("期数文本不能为空");
        }

        String text = periodText.trim();

        // 检查是否符合"第xxx期"的格式
        if (!text.startsWith("第") || !text.endsWith("期")) {
            throw new IllegalArgumentException("期数文本格式不正确，应为'第xxx期'的格式");
        }

        // 提取中间部分（去掉"第"和"期"）
        String middlePart = text.substring(1, text.length() - 1);

        if (middlePart.isEmpty()) {
            throw new IllegalArgumentException("期数文本中缺少数字部分");
        }

        return middlePart;
    }

    /**
     * 构造"第xxx期"格式的字符串
     * 与extractPeriodNumber方法配对使用
     *
     * @param periodNumber 期数（阿拉伯数字）
     * @param useChinese 是否使用中文数字格式
     * @return 格式化后的期数文本
     *
     * 示例：
     * buildPeriodText(1, true) -> "第一期"
     * buildPeriodText(10, false) -> "第10期"
     * buildPeriodText(23, true) -> "第二十三期"
     */
    public String buildPeriodText(int periodNumber, boolean useChinese) {
        if (periodNumber <= 0) {
            throw new IllegalArgumentException("期数必须大于0");
        }

        if (useChinese) {
            String chineseNumber = convertNumberToChinese(periodNumber);
            return "第" + chineseNumber + "期";
        } else {
            return "第" + periodNumber + "期";
        }
    }

    /**
     * 测试期数提取功能
     * 验证各种格式的期数文本解析
     */
    public void testPeriodExtraction() {
        System.out.println("=== 期数提取测试 ===");

        // 测试用例
        String[] testCases = {
            "第一期", "第二期", "第三期", "第十期", "第十一期", "第二十期",
            "第二十三期", "第一百期", "第一百零一期", "第一千期",
            "第1期", "第10期", "第23期", "第101期", "第1000期"
        };

        for (String testCase : testCases) {
            try {
                int number = extractPeriodNumber(testCase);
                String extracted = extractPeriodString(testCase);
                String rebuilt = buildPeriodText(number, true);

                System.out.printf("%-10s -> 数字: %-4d, 提取: %-6s, 重构: %s%n",
                    testCase, number, extracted, rebuilt);
            } catch (Exception e) {
                System.out.printf("%-10s -> 解析失败: %s%n", testCase, e.getMessage());
            }
        }

        // 测试异常情况
        System.out.println("\n=== 异常情况测试 ===");
        String[] errorCases = {
            "", "第期", "一期", "第一", "第abc期", "abc", null
        };

        for (String errorCase : errorCases) {
            try {
                int number = extractPeriodNumber(errorCase);
                System.out.printf("%-10s -> 意外成功: %d%n", errorCase, number);
            } catch (Exception e) {
                System.out.printf("%-10s -> 预期异常: %s%n",
                    (errorCase == null ? "null" : errorCase), e.getMessage());
            }
        }

        // 测试构造功能
        System.out.println("\n=== 期数构造测试 ===");
        int[] numbers = {1, 10, 23, 101, 1000};
        for (int num : numbers) {
            String chineseFormat = buildPeriodText(num, true);
            String arabicFormat = buildPeriodText(num, false);
            System.out.printf("数字 %d -> 中文: %s, 阿拉伯: %s%n",
                num, chineseFormat, arabicFormat);
        }
    }










    /**
     * 将阿拉伯数字转换为中文数字
     * 支持0-99999999的整数转换
     *
     * @param number 要转换的阿拉伯数字
     * @return 转换后的中文数字字符串
     *
     * 示例：
     * 0 -> 零
     * 10 -> 十
     * 101 -> 一百零一
     * 1234 -> 一千二百三十四
     * 10001 -> 一万零一
     */
    public String convertNumberToChinese(int number) {
        if (number < 0 || number > 99999999) {
            throw new IllegalArgumentException("数字超出支持范围，请输入0-99999999之间的整数");
        }

        if (number == 0) {
            return "零";
        }

        // 中文数字字符
        String[] chineseNumbers = {"", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        // 中文单位
        String[] units = {"", "十", "百", "千"};
        String[] bigUnits = {"", "万"};

        StringBuilder result = new StringBuilder();

        // 处理万位以上
        int wan = number / 10000;
        int ge = number % 10000;

        if (wan > 0) {
            result.append(convertThousands(wan, chineseNumbers, units));
            result.append("万");

            // 如果个位部分小于1000且大于0，需要加"零"
            if (ge > 0 && ge < 1000) {
                result.append("零");
            }
        }

        if (ge > 0) {
            result.append(convertThousands(ge, chineseNumbers, units));
        }

        String resultStr = result.toString();

        // 特殊处理：如果是10-19的数字，去掉开头的"一"
        if (number >= 10 && number <= 19 && resultStr.startsWith("一十")) {
            resultStr = resultStr.substring(1);
        }

        return resultStr;
    }

    /**
     * 转换千位以内的数字
     */
    private String convertThousands(int number, String[] chineseNumbers, String[] units) {
        if (number == 0) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        String numStr = String.valueOf(number);
        int length = numStr.length();
        boolean needZero = false;

        for (int i = 0; i < length; i++) {
            int digit = Character.getNumericValue(numStr.charAt(i));
            int position = length - i - 1; // 当前位置（从右往左：0=个位，1=十位，2=百位，3=千位）

            if (digit == 0) {
                // 如果当前位是0，标记需要添加零（但不立即添加）
                needZero = true;
            } else {
                // 如果前面有0且当前不是最高位，添加"零"
                if (needZero && result.length() > 0) {
                    result.append("零");
                }
                needZero = false;

                // 添加数字
                result.append(chineseNumbers[digit]);

                // 添加单位（个位不加单位）
                if (position > 0) {
                    result.append(units[position]);
                }
            }
        }

        return result.toString();
    }

    /**
     * 将中文数字转换为阿拉伯数字
     * 支持转换由convertNumberToChinese方法生成的中文数字
     *
     * @param chineseNumber 中文数字字符串
     * @return 转换后的阿拉伯数字
     *
     * 示例：
     * "零" -> 0
     * "十" -> 10
     * "一百零一" -> 101
     * "一千二百三十四" -> 1234
     * "一万零一" -> 10001
     */
    public int convertChineseToNumber(String chineseNumber) {
        if (chineseNumber == null || chineseNumber.trim().isEmpty()) {
            throw new IllegalArgumentException("中文数字不能为空");
        }

        chineseNumber = chineseNumber.trim();

        if ("零".equals(chineseNumber)) {
            return 0;
        }

        // 中文数字到阿拉伯数字的映射
        java.util.Map<Character, Integer> chineseToNumber = new java.util.HashMap<>();
        chineseToNumber.put('零', 0);
        chineseToNumber.put('一', 1);
        chineseToNumber.put('二', 2);
        chineseToNumber.put('三', 3);
        chineseToNumber.put('四', 4);
        chineseToNumber.put('五', 5);
        chineseToNumber.put('六', 6);
        chineseToNumber.put('七', 7);
        chineseToNumber.put('八', 8);
        chineseToNumber.put('九', 9);

        // 单位映射
        java.util.Map<Character, Integer> unitToNumber = new java.util.HashMap<>();
        unitToNumber.put('十', 10);
        unitToNumber.put('百', 100);
        unitToNumber.put('千', 1000);
        unitToNumber.put('万', 10000);

        int result = 0;
        int temp = 0;
        int wan = 0;

        // 处理特殊情况：以"十"开头的数字（如"十"、"十一"等）
        if (chineseNumber.startsWith("十")) {
            chineseNumber = "一" + chineseNumber;
        }

        for (int i = 0; i < chineseNumber.length(); i++) {
            char c = chineseNumber.charAt(i);

            if (chineseToNumber.containsKey(c)) {
                // 是数字
                int num = chineseToNumber.get(c);
                if (num == 0) {
                    // 遇到"零"，继续处理下一个字符
                    continue;
                }
                temp = num;
            } else if (unitToNumber.containsKey(c)) {
                // 是单位
                int unit = unitToNumber.get(c);

                if (unit == 10000) { // 万
                    wan = (result + temp) * unit;
                    result = 0;
                    temp = 0;
                } else {
                    result += temp * unit;
                    temp = 0;
                }
            }
        }

        // 处理最后剩余的数字
        result += temp;

        return wan + result;
    }

    /**
     * 智能数字转换方法
     * 自动判断输入是阿拉伯数字还是中文数字，并进行相应转换
     *
     * @param input 输入的数字字符串（阿拉伯数字或中文数字）
     * @return 转换结果的字符串表示
     */
    public String smartNumberConvert(String input) {
        if (input == null || input.trim().isEmpty()) {
            return "";
        }

        input = input.trim();

        // 判断是否为阿拉伯数字
        if (input.matches("\\d+")) {
            try {
                int number = Integer.parseInt(input);
                return convertNumberToChinese(number);
            } catch (NumberFormatException e) {
                return input; // 如果转换失败，返回原值
            }
        } else {
            // 判断是否包含中文数字字符
            if (input.matches(".*[零一二三四五六七八九十百千万].*")) {
                try {
                    int number = convertChineseToNumber(input);
                    return String.valueOf(number);
                } catch (Exception e) {
                    return input; // 如果转换失败，返回原值
                }
            }
        }

        return input; // 既不是阿拉伯数字也不是中文数字，返回原值
    }

    /**
     * 测试双向转换功能
     * 用于验证转换的正确性
     */
    public void testNumberConversion() {
        // 测试用例
        int[] testNumbers = {0, 1, 10, 11, 20, 101, 110, 1001, 1010, 1234, 10001, 50000, 123456};

        System.out.println("=== 数字转换测试 ===");
        for (int num : testNumbers) {
            String chinese = convertNumberToChinese(num);
            int backToNumber = convertChineseToNumber(chinese);
            boolean isCorrect = (num == backToNumber);

            System.out.printf("%d -> %s -> %d [%s]%n",
                num, chinese, backToNumber, isCorrect ? "✓" : "✗");
        }

        // 测试特殊情况
        System.out.println("\n=== 特殊情况测试 ===");
        String[] specialCases = {"十", "十一", "十九", "二十", "一百", "一千", "一万"};
        for (String chinese : specialCases) {
            try {
                int number = convertChineseToNumber(chinese);
                String backToChinese = convertNumberToChinese(number);
                System.out.printf("%s -> %d -> %s%n", chinese, number, backToChinese);
            } catch (Exception e) {
                System.out.printf("%s -> 转换失败: %s%n", chinese, e.getMessage());
            }
        }
    }
}




