package com.soft.gcc.xtbg.jygl.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 经营数据-催收管理
 * @TableName DFDW_JYSJ_Collection
 */
@TableName(value ="DFDW_JYSJ_Collection")
@Data
public class DfdwJysjCollection {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 催收期数term number
     */
    @TableField(value = "number_of_dunning_periods")
    private String numberOfDunningPeriods;

    /**
     * 催收日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    @JSONField(format="yyyy-MM-dd")
    @TableField(value = "collection_date")
    private Date collectionDate;

    /**
     * 最后催收日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    @JSONField(format="yyyy-MM-dd")
    @TableField(value = "last_dunning_date")
    private Date lastDunningDate;

    /**
     * 催收状态
     */
    @TableField(value = "state")
    private Integer state;

    /**
     * 邮件发送状态
     */
    @TableField(value = "email_status")
    private Integer emailStatus;

    /**
     * 确认状态
     */
    @TableField(value = "confirmation_status")
    private Integer confirmationStatus;

    /**
     * 应收账款数据表主键id
     */
    @TableField(value = "business_data_id")
    private Long businessDataId;

    /**
     * 附件id，用英文逗号拼接
     */
    @TableField(value = "annex_id")
    private String annexId;

    /**
     * 根据选中的type 生成对应的催收文件
     * 1:催办函
     * 2:工程审计请办函
     * 3:催款函
     * 4:往来款项催告函
     */
    @TableField(value = "annex_type")
    private Integer annexType;

    /**
     * 类型 1应收账款 2 隐性债券
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 佐证材料ids
     */
    @TableField(value = "support_docs_ids")
    private String supportDocsIds;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(value = "create_by")
    private String createBy;

    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 公司名称
     */
    @TableField(exist = false)
    private String companyName;
    /**
     * 项目名称
     */
    @TableField(exist = false)
    private String projectName;

    @TableField(exist = false)
    private Integer topGroupId;

    /**
     * 款项发生时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    @JSONField(format="yyyy-MM-dd")
    @TableField(exist = false)
    private Date transferTime;

    /**
     * 往来户单位
     */
    @TableField(exist = false)
    private String unit;





    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DfdwJysjCollection other = (DfdwJysjCollection) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getNumberOfDunningPeriods() == null ? other.getNumberOfDunningPeriods() == null : this.getNumberOfDunningPeriods().equals(other.getNumberOfDunningPeriods()))
                && (this.getCollectionDate() == null ? other.getCollectionDate() == null : this.getCollectionDate().equals(other.getCollectionDate()))
                && (this.getLastDunningDate() == null ? other.getLastDunningDate() == null : this.getLastDunningDate().equals(other.getLastDunningDate()))
                && (this.getState() == null ? other.getState() == null : this.getState().equals(other.getState()))
                && (this.getEmailStatus() == null ? other.getEmailStatus() == null : this.getEmailStatus().equals(other.getEmailStatus()))
                && (this.getConfirmationStatus() == null ? other.getConfirmationStatus() == null : this.getConfirmationStatus().equals(other.getConfirmationStatus()))
                && (this.getBusinessDataId() == null ? other.getBusinessDataId() == null : this.getBusinessDataId().equals(other.getBusinessDataId()))
                && (this.getAnnexId() == null ? other.getAnnexId() == null : this.getAnnexId().equals(other.getAnnexId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getNumberOfDunningPeriods() == null) ? 0 : getNumberOfDunningPeriods().hashCode());
        result = prime * result + ((getCollectionDate() == null) ? 0 : getCollectionDate().hashCode());
        result = prime * result + ((getLastDunningDate() == null) ? 0 : getLastDunningDate().hashCode());
        result = prime * result + ((getState() == null) ? 0 : getState().hashCode());
        result = prime * result + ((getEmailStatus() == null) ? 0 : getEmailStatus().hashCode());
        result = prime * result + ((getConfirmationStatus() == null) ? 0 : getConfirmationStatus().hashCode());
        result = prime * result + ((getBusinessDataId() == null) ? 0 : getBusinessDataId().hashCode());
        result = prime * result + ((getAnnexId() == null) ? 0 : getAnnexId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", numberOfDunningPeriods=").append(numberOfDunningPeriods);
        sb.append(", collectionDate=").append(collectionDate);
        sb.append(", lastDunningDate=").append(lastDunningDate);
        sb.append(", state=").append(state);
        sb.append(", emailStatus=").append(emailStatus);
        sb.append(", confirmationStatus=").append(confirmationStatus);
        sb.append(", businessDataId=").append(businessDataId);
        sb.append(", annexId=").append(annexId);
        sb.append("]");
        return sb.toString();
    }
}