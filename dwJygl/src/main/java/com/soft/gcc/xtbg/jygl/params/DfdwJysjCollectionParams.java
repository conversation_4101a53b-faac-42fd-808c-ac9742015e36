package com.soft.gcc.xtbg.jygl.params;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class DfdwJysjCollectionParams  implements Serializable {

    private Long id;

    /**
     * 类型 1 应收账款 2 隐性债券
     */
    private Integer type;
    /**
     * 公司名称
     */

    private String companyName;
    /**
     * 项目名称
     */

    private String projectName;

    private Integer topGroupId;

    /**
     * 款项发生时间
     */
    private Date transferTime;

    /**
     * 往来户单位
     */
    private String unit;

    /**
     * 催收状态
     */
    private Integer state;

    /**
     * 邮件发送状态
     */
    private Integer emailStatus;

    /**
     * 确认状态
     */
    private Integer confirmationStatus;


    /**
     * 是否压缩为ZIP文件
     */
    private Boolean isZipCompressed;

    /**
     * docx邮件模板文件ID
     */
    private String docxFileId;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 每页数量
     */
    private Integer pageSize;

    /**
     * 总记录数
     */
    private Integer total;
}
