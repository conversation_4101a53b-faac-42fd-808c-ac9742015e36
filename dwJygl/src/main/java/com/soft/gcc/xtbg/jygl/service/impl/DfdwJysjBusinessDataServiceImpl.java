package com.soft.gcc.xtbg.jygl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.helper.SessionHelper;
import com.soft.gcc.common.groupitem.entity.Groupitem;
import com.soft.gcc.common.groupitem.service.GroupitemService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.jygl.dto.AccountsReceivableDto;
import com.soft.gcc.xtbg.jygl.dto.ProjectNameAndCodeDto;
import com.soft.gcc.xtbg.jygl.entity.DfdwJysjBusinessData;
import com.soft.gcc.xtbg.jygl.mapper.DfdwJysjBusinessDataMapper;
import com.soft.gcc.xtbg.jygl.params.DfdwJysjBusinessDataParam;
import com.soft.gcc.xtbg.jygl.service.DfdwJysjBusinessDataService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【DFDW_JYSJ_Business_data(经营数据-经营数据管理)】的数据库操作Service实现
* @createDate 2025-07-15 14:11:24
*/
@Service
public class DfdwJysjBusinessDataServiceImpl extends ServiceImpl<DfdwJysjBusinessDataMapper, DfdwJysjBusinessData>
    implements DfdwJysjBusinessDataService{

    @Resource
    GroupitemService groupitemService;




    @Override
    public IPage<DfdwJysjBusinessData> getList(DfdwJysjBusinessDataParam param) {
        //总管理看全部，部门管理员看自己部门
//        SessionHelper.getSessionPerson();
        boolean isNotAdmin = true;
        Integer deptId = SessionHelper.getSessionPerson().getTopGroupId();
        if(SessionHelper.getSessionPerson().getRoleList().contains("经营分析-隐性债权-部门管理")){
            isNotAdmin = true;
        }
        if(SessionHelper.getSessionPerson().getRoleList().contains("经营分析-隐性债权-总管理")){
            isNotAdmin = false;
        }

        IPage<DfdwJysjBusinessData> page = new Page<>(param.getPageNum(), param.getPageSize());
        return page(page, new LambdaQueryWrapper<DfdwJysjBusinessData>()
                .like(StringUtils.isNotBlank(param.getCompanyName()) && !"其他".equals(param.getCompanyName())
                        && param.getIsSummary() == 0
                        ,

                        DfdwJysjBusinessData::getCompanyName,param.getCompanyName()
                )
                .eq(StringUtils.isNotBlank(param.getCompanyName()) && !"其他".equals(param.getCompanyName())
                                && param.getIsSummary() == 1
                        ,

                        DfdwJysjBusinessData::getCompanyName,param.getCompanyName()
                )
                .isNull("其他".equals(param.getCompanyName()),DfdwJysjBusinessData::getTopGroupId)
                //.like(StringUtils.isNotBlank(param.getCompanyName()),DfdwJysjBusinessData::getCompanyName,param.getCompanyName())
                .like(StringUtils.isNotBlank(param.getProjectCode()),DfdwJysjBusinessData::getProjectCode,param.getProjectCode())
                .like(StringUtils.isNotBlank(param.getProjectName()),DfdwJysjBusinessData::getProjectName,param.getProjectName())
                .eq(isNotAdmin,DfdwJysjBusinessData::getTopGroupId,deptId)
                .orderByDesc(DfdwJysjBusinessData::getTransferTime)
        );
    }


    @Override
    public List<ProjectNameAndCodeDto> getProjectNameAndCode() {
        return this.baseMapper.getProjectNameAndCode();
    }


    @Override
    public List<AccountsReceivableDto> getSummary() {
        Integer deptId = SessionHelper.getSessionPerson().getTopGroupId();

        if(SessionHelper.getSessionPerson().getRoleList().contains("经营分析-隐性债权-总管理")){
            deptId = null;
        }
        return this.baseMapper.getSummary(deptId);
    }

    @Override
    public Result<Object> importExcel(MultipartFile file) {
        try {
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook(file.getInputStream());
            XSSFSheet sheet = xssfWorkbook.getSheetAt(0);

            // 验证Excel是否有数据
            if (sheet.getLastRowNum() < 1) {
                return Result.error("Excel文件没有数据行");
            }

            // 验证标题行
            XSSFRow headerRow = sheet.getRow(0);
            String headerValidationResult = validateHeaders(headerRow);
            if (headerValidationResult != null) {
                return Result.error(headerValidationResult);
            }

            //excel中的数据
            List<DfdwJysjBusinessData> excelList = new ArrayList<>();

            //获取部门数据
            List<Groupitem> groupItemList = groupitemService.list();
            //数据从第二行开始，第一行是标题
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                XSSFRow row = sheet.getRow(i);
                if (row == null) {
                    continue; // 跳过空行
                }

                // 检查行是否为空行（所有单元格都为空）
                if (isEmptyRow(row)) {
                    continue; // 跳过空行
                }

                DfdwJysjBusinessData entity = parseRowToEntity(row, i + 1,groupItemList);
                if (entity != null) {
                    excelList.add(entity);
                }
            }
            // 进行数据库操作
            if (!excelList.isEmpty()) {
                // 获取数据库中现有数据
                List<DfdwJysjBusinessData> debtmanagementList = list();

                // 分离新增和修改数据
                List<DfdwJysjBusinessData> insertList = new ArrayList<>();
                List<DfdwJysjBusinessData> updateList = new ArrayList<>();

                // 创建"项目编号+凭证号"组合键的Map，提高查找效率
                Map<String, DfdwJysjBusinessData> existingDataMap = debtmanagementList.stream()
                        .filter(item -> StringUtils.isNotBlank(item.getProjectCode()) && StringUtils.isNotBlank(item.getTransferVoucher()))
                        .collect(Collectors.toMap(
                                item -> item.getProjectCode() + "|" + item.getTransferVoucher(), // 使用项目编号+凭证号作为key
                                item -> item,
                                (v1, v2) -> v1
                        ));

                // 遍历Excel数据，判断新增还是修改
                // 用于记录Excel中存在的组合键
                Set<String> excelCompositeKeys = new HashSet<>();

                for (DfdwJysjBusinessData excelItem : excelList) {
                    if (StringUtils.isNotBlank(excelItem.getProjectCode()) && StringUtils.isNotBlank(excelItem.getTransferVoucher())) {
                        String compositeKey = excelItem.getProjectCode() + "|" + excelItem.getTransferVoucher();
                        excelCompositeKeys.add(compositeKey);

                        if (existingDataMap.containsKey(compositeKey)) {
                            // 存在于数据库中，设置ID后放入修改列表
                            DfdwJysjBusinessData existingItem = existingDataMap.get(compositeKey);
                            // 导入金额与上次进行对比，计入收回金额以及收回时间（导入时间）
                            if (existingItem.getMoney().compareTo(excelItem.getMoney()) > 0) {
                                // 金额减少，说明客户还款了
                                excelItem.setRecoveredAmount(existingItem.getMoney().subtract(excelItem.getMoney()));
                                excelItem.setRecoveredTime(new Date());
                            }
                            excelItem.setId(existingItem.getId());
                            //默认状态给他打开
                            excelItem.setStatus(0);
                            updateList.add(excelItem);
                        } else {
                            // 不存在于数据库中，清空ID后放入新增列表
                            excelItem.setId(null);
                            excelItem.setStatus(0); // 新增数据默认状态为0
                            insertList.add(excelItem);
                        }
                    }
                }

                // 处理数据库中存在但Excel中没有的数据，设置status为1
                for (Map.Entry<String, DfdwJysjBusinessData> entry : existingDataMap.entrySet()) {
                    String compositeKey = entry.getKey();
                    if (!excelCompositeKeys.contains(compositeKey)) {
                        // 数据库存在但Excel中没有，设置status为1
                        DfdwJysjBusinessData existingItem = entry.getValue();
                        existingItem.setStatus(1);
                        updateList.add(existingItem);
                    }
                }

                // 执行数据库操作
                int insertCount = 0;
                int updateCount = 0;

                // 批量新增（每次50条，SQL Server参数限制）
                if (!insertList.isEmpty()) {
                    batchInsert(insertList);
                    insertCount = insertList.size();
                }

                // 批量修改
                if (!updateList.isEmpty()) {
                    updateBatchById(updateList);
                    updateCount = updateList.size();
                }

                return Result.ok("导入成功! 新增" + insertCount + "条，修改" + updateCount + "条数据");
            } else {
                return Result.error("没有有效的数据可导入");
            }

        } catch (Exception e) {
            log.error("应收账款数据管理导入失败:" + e, e);
            return Result.error("导入失败：" + e);
        }
    }






    /**
     * 验证Excel标题行是否正确
     */
    private String validateHeaders(XSSFRow headerRow) {
        if (headerRow == null) {
            return "标题行不能为空";
        }

        // 定义期望的标题列表（按顺序）
        String[] expectedHeaders = {
                "公司名称","单位", "科目编号", "科目名称", "客户档案编号", "客户档案", "项目类型编号", "项目类型名称",
                "项目编号", "项目名称", "凭证号", "金额", "款项发生时间", "账龄(天数)",
                "账龄时间分段", "单位来源", "客商分类", "添加年月", "清除年月", "摘要"
        };

        // 检查标题数量
        if (headerRow.getLastCellNum() < expectedHeaders.length) {
            return "Excel标题列数不足，期望" + expectedHeaders.length + "列，实际" + headerRow.getLastCellNum() + "列";
        }

        // 检查每个标题是否匹配
        for (int i = 0; i < expectedHeaders.length; i++) {
            Cell cell = headerRow.getCell(i);
            String actualHeader = getCellStringValue(cell);
            if (!expectedHeaders[i].equals(actualHeader)) {
                return "第" + (i + 1) + "列标题不匹配，期望：" + expectedHeaders[i] + "，实际：" + actualHeader;
            }
        }

        return null; // 验证通过
    }

    /**
     * 将Excel行数据解析为实体对象
     */
    private DfdwJysjBusinessData parseRowToEntity(XSSFRow row, int rowNum,List<Groupitem> groupItemList) {
        DfdwJysjBusinessData entity = new DfdwJysjBusinessData();
        //公司名称	科目编号	科目名称	客户档案编号	客户档案	项目类型编号	项目类型名称	项目编号	项目名称	凭证号	金额	款项发生时间	账龄(天数)	账龄时间分段	单位来源	客商分类	添加年月	清除年月	摘要

        // 按列顺序解析数据
        entity.setCompanyName(getCellStringValue(row.getCell(0), rowNum, "公司名称").trim());
//        groupItemList.stream().filter(item -> item.getUcomapanyqc().equals(entity.getCompanyName())).findFirst().ifPresent(item -> entity.setTopGroupId(item.getId()));
        //设置单位  和 部门id
        Groupitem groupitem = groupItemList.stream().filter(item -> item.getUcomapanyqc().equals(entity.getCompanyName())).findFirst().orElse(null);
        if (groupitem != null) {
           // entity.setUnit(groupitem.getGroupname());
            entity.setTopGroupId(groupitem.getId());
        }
        entity.setUnit(getCellStringValue(row.getCell(1), rowNum, "单位"));
        entity.setSubjectNumber(getCellStringValue(row.getCell(2), rowNum, "科目编号"));
        entity.setSubjectName(getCellStringValue(row.getCell(3), rowNum, "科目名称"));
        entity.setCustomerFileNumber(getCellStringValue(row.getCell(4), rowNum, "客户档案编号"));
        entity.setCustomer(getCellStringValue(row.getCell(5), rowNum, "客户档案"));
        entity.setProjectTypeCode(getCellStringValue(row.getCell(6), rowNum, "项目类型编号"));
        entity.setProjectTypeName(getCellStringValue(row.getCell(7), rowNum, "项目类型名称"));
        entity.setProjectCode(getCellStringValue(row.getCell(8), rowNum, "项目编号"));
        entity.setProjectName(getCellStringValue(row.getCell(9), rowNum, "项目名称"));
        entity.setTransferVoucher(getCellStringValue(row.getCell(10), rowNum, "凭证号"));

        entity.setMoney(new BigDecimal(getCellStringValue(row.getCell(11), rowNum, "金额")));
        entity.setTransferTime(parseDate(getCellStringValue(row.getCell(12), rowNum, "款项发生时间"), rowNum, "款项发生时间"));
        entity.setAgingOfAccounts(getCellStringValue(row.getCell(13), rowNum, "账龄(天数)"));
        entity.setAgingBuckets(getCellStringValue(row.getCell(14), rowNum, "账龄时间分段"));

        entity.setFundingSource(getCellStringValue(row.getCell(15), rowNum, "单位来源"));
        entity.setBusinessPartnerClassification(getCellStringValue(row.getCell(16), rowNum, "客商分类"));
        entity.setAddTime(parseDateYm(getCellStringValue(row.getCell(17), rowNum, "添加年月"), rowNum, "添加年月"));
        entity.setRemoveTime(parseDateYm(getCellStringValue(row.getCell(18), rowNum, "清除年月"), rowNum, "清除年月"));
        entity.setRemark(getCellStringValue(row.getCell(19), rowNum, "摘要"));

        return entity;
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        try {
            switch (cell.getCellType()) {
                case STRING:
                    return StringUtils.trimToNull(cell.getStringCellValue());
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
                        return sdf.format(cell.getDateCellValue());
                    } else {
                        // 数字转字符串，避免科学计数法
                        double numericValue = cell.getNumericCellValue();
                        if (numericValue == (long) numericValue) {
                            return String.valueOf((long) numericValue);
                        } else {
                            return String.valueOf(numericValue);
                        }
                    }
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    // 公式单元格，尝试获取计算结果
                    try {
                        return StringUtils.trimToNull(cell.getStringCellValue());
                    } catch (Exception e) {
                        try {
                            double numericValue = cell.getNumericCellValue();
                            if (numericValue == (long) numericValue) {
                                return String.valueOf((long) numericValue);
                            } else {
                                return String.valueOf(numericValue);
                            }
                        } catch (Exception ex) {
                            return null;
                        }
                    }
                default:
                    return null;
            }
        } catch (Exception e) {
            throw new RuntimeException("单元格读取失败");
        }
    }

    /**
     * 获取单元格字符串值（带错误定位）
     */
    private String getCellStringValue(Cell cell, int rowNum, String fieldName) {
        try {
            return getCellStringValue(cell);
        } catch (Exception e) {
            throw new RuntimeException("第" + rowNum + "行【" + fieldName + "】字段格式错误");
        }
    }

    /**
     * 解析日期字符串
     */
    private Date parseDate(String dateStr, int rowNum, String fieldName) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }

        // 按优先级尝试四种日期格式
        String[] dateFormats = {"yyyy/MM/dd", "yyyy-MM-dd", "yyyy/MM/dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss"};
        for (String format : dateFormats) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(format);
                return sdf.parse(dateStr.trim());
            } catch (Exception ignored) {
                // 继续尝试下一种格式
            }
        }

        // 所有格式都失败，抛出异常
        throw new RuntimeException("第" + rowNum + "行【" + fieldName + "】字段格式错误");
    }

    private Date parseDateYm(String dateStr, int rowNum, String fieldName) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }

        // 按优先级尝试四种日期格式
        String[] dateFormats = {"yyyy-MM","yyyy/MM"};
        for (String format : dateFormats) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(format);
                return sdf.parse(dateStr.trim());
            } catch (Exception ignored) {
                // 继续尝试下一种格式
            }
        }

        // 所有格式都失败，抛出异常
        throw new RuntimeException("第" + rowNum + "行【" + fieldName + "】字段格式错误");
    }

    /**
     * 解析BigDecimal字符串
     */
    private BigDecimal parseBigDecimal(String numStr, int rowNum, String fieldName) {
        if (StringUtils.isBlank(numStr)) {
            return null;
        }

        try {
            // 移除可能的货币符号和逗号
            String cleanStr = numStr.trim().replaceAll("[￥$,]", "");
            return new BigDecimal(cleanStr);
        } catch (Exception e) {
            throw new RuntimeException("第" + rowNum + "行【" + fieldName + "】字段格式错误");
        }
    }

    /**
     * 解析布尔值
     */
    private Boolean parseBoolean(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }

        value = value.trim();
        if ("是".equals(value)) {
            return true;
        } else if ("否".equals(value)) {
            return false;
        }

        return null;
    }

    /**
     * 判断行是否为空行
     */
    private boolean isEmptyRow(XSSFRow row) {
        if (row == null) {
            return true;
        }

        // 检查前20列（我们需要的列数），如果都为空则认为是空行
        for (int i = 0; i < 20; i++) {
            Cell cell = row.getCell(i);
            if (cell != null && StringUtils.isNotBlank(getCellStringValue(cell))) {
                return false; // 有非空单元格，不是空行
            }
        }

        return true; // 所有单元格都为空，是空行
    }

    /**
     * 批量插入数据，每次100条（SQL Server参数限制2100个，21字段*50条=1000个参数）
     */
    private void batchInsert(List<DfdwJysjBusinessData> insertList) {
        int batchSize = 50;  // SQL Server参数限制，21字段*50条=2000个参数 < 2100
        for (int i = 0; i < insertList.size(); i += batchSize) {
            List<DfdwJysjBusinessData> batchList = insertList.subList(i, Math.min(i + batchSize, insertList.size()));
            baseMapper.batchInsert(batchList);
        }
    }



    @Override
    public List<String> getCompanyOptions(PersonEntity user) {
        boolean allAdmin = false;
        for (String roleVO : user.getRoleList()) {
            if ("经营分析-隐性债权-总管理".equals(roleVO)) {
                allAdmin = true;
                break;
            }
        }
        return baseMapper.getCompanyOptions(allAdmin,user.getTopGroupId());
    }
}




