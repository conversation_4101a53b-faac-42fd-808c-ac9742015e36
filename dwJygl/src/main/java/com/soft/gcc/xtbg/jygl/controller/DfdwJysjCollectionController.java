package com.soft.gcc.xtbg.jygl.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.jygl.entity.DfdwJysjCollection;
import com.soft.gcc.xtbg.jygl.params.DfdwJysjCollectionParams;
import com.soft.gcc.xtbg.jygl.service.DfdwJysjCollectionService;
import org.apache.catalina.LifecycleState;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 催收管理
 */
@RestController
@RequestMapping("collection")
public class DfdwJysjCollectionController extends BaseController {
    @Resource
    DfdwJysjCollectionService collectionService;


//JDWJY01CSGL01QX01	隐性债权-催收管理	查看
//JDWJY01CSGL01QX02	隐性债权-催收管理	发送邮件
//JDWJY01CSGL01QX03	隐性债权-催收管理	确认
    @PostMapping("/getList")
    public Result<?> getList(@RequestBody DfdwJysjCollectionParams param){
        if (user() == null){
            return Result.error("用户未登录");
        }
        return Result.ok(collectionService.pageList(param));
    }

    @PostMapping("getInfo")
    public Result<?> getInfo(@RequestBody DfdwJysjCollectionParams param){
        //设置 -1 让mybatis-plus 不分页
        param.setPageSize(-1);
        param.setPageNum(1);
        List<DfdwJysjCollection> collection = collectionService.pageList(param).getRecords();
        if (collection == null || collection.size() == 0){
            return Result.error("未找到数据");
        }
        return Result.ok(collection.get(0));
    }

    @PostMapping("add")
    public Result<?> add(@RequestBody DfdwJysjCollection param) throws IOException {
        if (user() == null){
            return Result.error("用户未登录");
        }
        return Result.ok(collectionService.addOrUpdate(param));
    }

    @PostMapping("update")
    public Result<?> update(@RequestBody DfdwJysjCollection param) throws IOException {
        if (user() == null){
            return Result.error("用户未登录");
        }
        return Result.ok(collectionService.addOrUpdate(param));
    }


    @GetMapping("/generateDocument/{collectionId}")
    public void generateDocument(@PathVariable Integer collectionId, HttpServletResponse response) throws IOException {
       // collectionService.dunningLetter(collectionId, response);
    }



    @PostMapping("sendEmail")
    public Result<?> sendEmail(@RequestBody DfdwJysjCollectionParams param) throws IOException {
        collectionService.sendEmail(param);
        return Result.ok();
    }

    /**
     * 确认
     * @param param
     * @return
     */
    @PostMapping("confirm")
    public Result<?> confirm(@RequestBody DfdwJysjCollectionParams param){
        collectionService.update(new LambdaUpdateWrapper<DfdwJysjCollection>()
                .set(DfdwJysjCollection::getConfirmationStatus,1)
                .eq(DfdwJysjCollection::getId, param.getId())
        );
        return Result.ok();
    }



















}
