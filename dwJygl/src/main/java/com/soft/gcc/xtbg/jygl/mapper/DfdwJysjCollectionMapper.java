package com.soft.gcc.xtbg.jygl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.jygl.entity.DfdwJysjCollection;
import com.soft.gcc.xtbg.jygl.params.DfdwJysjCollectionParams;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【DFDW_JYSJ_Collection(经营数据-催收管理)】的数据库操作Mapper
* @createDate 2025-07-15 14:13:11
* @Entity com.soft.gcc.xtbg.jygl.entity.DfdwJysjCollection
*/
public interface DfdwJysjCollectionMapper extends BaseMapper<DfdwJysjCollection> {


    IPage<DfdwJysjCollection> pageList(Page<DfdwJysjCollection> page, @Param("query") DfdwJysjCollectionParams param);

    IPage<DfdwJysjCollection> pageList1(Page<DfdwJysjCollection> page, @Param("query") DfdwJysjCollectionParams param);

}




