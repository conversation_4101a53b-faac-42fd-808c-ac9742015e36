package com.soft.gcc.xtbg.jygl.controller;

import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.jygl.entity.DfdwJysjAssociated;
import com.soft.gcc.xtbg.jygl.params.DebtmanagementParams;
import com.soft.gcc.xtbg.jygl.service.DfdwJysjAssociatedService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@RequestMapping("/associated")
public class AssociatedController extends BaseController {
    @Resource
    private DfdwJysjAssociatedService associatedService;
    @PostMapping("/getList")
    public Result<Object> getList(@RequestBody DebtmanagementParams params){
        return Result.ok(associatedService.getList(params));
    }

    @PostMapping("/add")
    public Result<Object> add(@RequestBody DfdwJysjAssociated associated){
        return Result.ok(associatedService.save(associated));
    }

    @PostMapping("/update")
    public Result<Object> update(@RequestBody DfdwJysjAssociated associated){
        return Result.ok(associatedService.updateById(associated));
    }

    @DeleteMapping("/delete/{id}")
    public Result<Object> delete(@PathVariable("id") Integer id){
        return Result.ok(associatedService.removeById(id));
    }

    @GetMapping("/getById/{id}")
    public Result<Object> getById(@PathVariable("id") Integer id){
        return Result.ok(associatedService.getById(id));
    }

    /**
     * 导出Excel模板
     * @param response
     */
    @GetMapping("/exportExcelTemplate")
    public void exportExcelTemplate( HttpServletResponse response) throws IOException {
        associatedService.exportExcelTemplate(response);
    }


    /**
     * 上传
     */
    @PostMapping("/importExcel")
    public Result<?> importExcel(MultipartFile file){
        return associatedService.importExcel(file);
    }
}
