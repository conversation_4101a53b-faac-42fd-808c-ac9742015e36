package com.soft.framework.controller.BaseOpt;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.security.Md5Util;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.config.BaseConfig;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.yyszc.extend.DataTable;
import com.yyszc.wpbase.entity.Person;
import com.yyszc.wpbase.entity.vGroupItem;
import com.yyszc.wpbase.entity.vPerson;
import com.yyszc.wpbase.ventity.PersonEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Controller
@Configuration
@RequestMapping(value = "/Service/BaseOpt/UserMan")
@Api(tags = "基本框架接口->用户管理接口")
public class UserManController {
    private String defaultpwd = Md5Util.getMD5Str("zpepc001@@"); //"w1A9rdnf1rwQJf0EZwwzdA==";

    private Boolean GatherParams2Obj(Map<String, String> params, Person entity, StringBuilder msgstr) {
        try {
            if (!StringUtil.IsNull(params.get("LoginName"))) {
                entity.setLoginName(params.get("LoginName"));
            }
            if (!StringUtil.IsNull(params.get("RealName"))) {
                entity.setRealName(params.get("RealName"));
            }
            if (!StringUtil.IsNull(params.get("MsgType"))) {
                entity.setMsgType(params.get("MsgType"));
            }
            if (!StringUtil.IsNull(params.get("OA"))) {
                entity.setOA(params.get("OA"));
            }
            if (!StringUtil.IsNull(params.get("Telephone"))) {
                entity.setTelephone(params.get("Telephone"));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("GroupID"))) {
                entity.setGroupID(Integer.parseInt(params.get("GroupID")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("type"))) {
                entity.setType(Integer.parseInt(params.get("type")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("P_XH"))) {
                entity.setP_XH(Integer.parseInt(params.get("P_XH")));
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("程序产生异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/AddUser", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "AddUser", notes = "新增用户接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult AddUser(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            StringBuilder msgstr = new StringBuilder();

            Person entity = new Person();

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，获取传输参数失败！");
                return ajaxResult;
            }
            entity.setPassword(defaultpwd);

            Integer uid = -1;
            uid = WpServiceHelper.AddPerson(entity);
            if (uid == null || uid == -1) {
                ajaxResult = AjaxResult.error("操作失败，新增用户信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success(String.valueOf(uid));
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ModifyUser", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ModifyUser", notes = "修改用户信息接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult ModifyUser(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            StringBuilder msgstr = new StringBuilder();

            String UserId = params.get("UserId");
            Integer iid = Integer.parseInt(UserId);

            Person entity = null;

            entity = WpServiceHelper.GetPersonById(iid);
            if (entity == null) {
                ajaxResult = AjaxResult.error("操作失败，获取用户信息失败！");
                return ajaxResult;
            }

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，参数传输失败！");
                return ajaxResult;
            }

            Boolean uflag = false;
            uflag = WpServiceHelper.UpdatePerson(entity);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，修改用户信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success(entity.getId().toString());
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/DeleteUser", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "DeleteUser", notes = "删除用户接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult DeleteUser(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try {
            String UserId = request.getParameter("UserId");
            int iuserid = Integer.parseInt(UserId);

            Boolean uflag = false;

            uflag = WpServiceHelper.DeletePersonById(iuserid);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，删除用户信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success("删除用户信息成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }


    private boolean GetUIQueryString(HttpServletRequest request, StringBuilder strsql, StringBuilder orderstr, StringBuilder msgstr) {
        try {
            String LoginName = request.getParameter("LoginName");
            String RealName = request.getParameter("RealName");
            String GroupId = request.getParameter("GroupId");
            String CompName = request.getParameter("CompName");
            String GroupName = request.getParameter("GroupName");

            int togGroupId = SessionHelper.getSessionTopGroupId();
            if (togGroupId != 1) {
                strsql.append("select * from vPerson where TopGroupId='" + togGroupId + "'");
            } else {
                strsql.append("select * from vPerson where 1=1 ");
            }
            if (!StringUtil.IsNullOrEmpty(GroupId)) {
                strsql.append(" and GroupId=" + GroupId);
            }
            if (!StringUtil.IsNullOrEmpty(LoginName)) {
                strsql.append(" and LoginName like '%" + LoginName + "%'");
            }
            if (!StringUtil.IsNullOrEmpty(RealName)) {
                strsql.append(" and RealName like '%" + RealName + "%'");
            }
            if (!StringUtil.IsNullOrEmpty(CompName)) {
                strsql.append(" and TopGroupName like '%" + CompName + "%'");
            }
            if (!StringUtil.IsNullOrEmpty(GroupName)) {
                strsql.append(" and GroupName like '%" + GroupName + "%'");
            }

            if (BaseConfig.getExtVersion().equals("3.2")) {
                String sortf = request.getParameter("sort");
                String sortd = request.getParameter("dir");
                if (!StringUtil.IsNullOrEmpty(sortf) && !StringUtil.IsNullOrEmpty(sortd)) {
                    orderstr.append(" order by " + sortf + " " + sortd);
                }
            } else {
                String jsons = request.getParameter("sort");
                ToolHelper.DeserializeExtJsSortInfo(jsons, orderstr);
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("系统异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/GetUserList", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetUserList", notes = "获取用户列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult GetUserList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();

        //参数获取
        String tmpstr = "";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = StringUtil.IsNullOrEmpty(starts) ? 0 : Integer.parseInt(starts);
        int limit = StringUtil.IsNullOrEmpty(limits) ? 20 : Integer.parseInt(limits);

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        String strsql = "";
        int rcount = 0;

        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return ajaxResult;
        }

        try {
            String rcsql = DBHelper.ToRecordCountSql(basesql.toString());

            String rrval = "0";
            rrval = WpServiceHelper.GetRecordCount(rcsql);
            if (rrval == null || rrval.equals("")) {
                ajaxResult = AjaxResult.error("操作失败，获取数据条目信息失败！");
                return ajaxResult;
            }

            if (!rrval.equals("")) {
                rcount = Integer.parseInt(rrval);
            }

            int pageCount = (rcount / limit) + 1;
            int currpage = start / limit;
            strsql = sqlhelper.ToPageSql(basesql.toString(), orderstr.toString(), limit, currpage);

            List<vPerson> list = null;
            list = WpServiceHelper.GetVPersonList(strsql);
            if (list == null) {
                ajaxResult = AjaxResult.error("操作失败，获取用户信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.extgrid(vPerson.class, rcount, list);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }


    @RequestMapping(value = "/GetGroupList", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetGroupList", notes = "获取组织机构列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetGroupList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        try {
            List<vGroupItem> list = WpServiceHelper.GetGroupList();
            ajaxResult = AjaxResult.extgrid(vGroupItem.class, list.size(), list);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/GetUserById", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetUserById", notes = "获取指定具有指定ID的用户信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetUserById(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        String UserId = request.getParameter("UserId");
        if (StringUtil.IsNullOrEmpty(UserId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            Integer iuserid = Integer.parseInt(UserId);

            vPerson entity = null;

            entity = WpServiceHelper.GetVPersonById(iuserid);
            if (entity == null) {
                ajaxResult = AjaxResult.error("操作失败，获取用户信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.extform(vPerson.class, "获取信息成功！", entity);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ExportExcel", produces = {"text/plain;charset=UTF-8"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ExportExcel", notes = "导出用户信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String ExportExcel(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return JSON.toJSON(ajaxResult).toString();
        }

        try {
            DataTable dt = null;

            dt = WpServiceHelper.GetDataTable(basesql + " " + orderstr);
            if (dt == null) {
                ajaxResult = AjaxResult.error("操作失败，获取用户信息失败！");
                return JSON.toJSON(ajaxResult).toString();
            }

            StringBuilder fname = new StringBuilder();
            StringBuilder retstr = new StringBuilder();
            List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<ToolHelper.ExportColumnMode>();
            cmlist.add(new ToolHelper.ExportColumnMode("LoginName", "登录账号", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("RealName", "用户名称", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("TopGroupName", "所属公司", 40));
            cmlist.add(new ToolHelper.ExportColumnMode("GroupName", "所属部门", 40));
            cmlist.add(new ToolHelper.ExportColumnMode("Telephone", "电话号码", 40));
            boolean retb = ToolHelper.ExportDS2XlsFile(dt, "用户信息", cmlist, retstr, fname);
            if (retb) {
                ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
                cfs.success = true;
                cfs.text = "生成文件成功！";
                cfs.fname = fname.toString();
                cfs.data1 = ToolHelper.File2Bytes(ConfigHelper.getTempPath() + ConfigHelper.getfSepChar() + fname.toString());
                cfs.data2 = null;

                String jsonstr = JSON.toJSON(cfs).toString();
                return jsonstr;
            } else {
                ajaxResult = AjaxResult.error("导出文件异常!");
                return JSON.toJSON(ajaxResult).toString();
            }
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }

    @RequestMapping(value = "/ResetPassword", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ResetPassword", notes = "重置指定用户密码信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult ResetPassword(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try {
            String UserId = request.getParameter("UserId");
            Integer iuserid = Integer.parseInt(UserId);

            Boolean uflag = false;
            uflag = WpServiceHelper.ResetPassword(iuserid, defaultpwd);
            if (uflag == false) {
                ajaxResult = AjaxResult.error("操作失败，重置用户密码失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success("密码重置成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/CheckOldPassword", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "CheckOldPassword", notes = "验证当前用户密码信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult CheckOldPassword(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try {
            PersonEntity person = SessionHelper.getSessionPerson();

            String OldPwd = request.getParameter("OldPwd");
            String MD5Password = Md5Util.getMD5Str(OldPwd);

            if (MD5Password.equals(person.getPassword())) {
                ajaxResult = AjaxResult.success("验证旧密码成功！");
                return ajaxResult;
            } else {
                ajaxResult = AjaxResult.success("验证旧密码失败！");
                return ajaxResult;
            }
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/UpdatePassword", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "UpdatePassword", notes = "修改当前用户密码信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult UpdatePassword(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try {
            PersonEntity person = SessionHelper.getSessionPerson();

            String NewPwd = request.getParameter("NewPwd");
            String MD5Password = Md5Util.getMD5Str(NewPwd);
            String ypassword = request.getParameter("ypassword");

            Boolean success = false;
            String text = "";
            Boolean usernameSuccess = false;
            String passwordError = "";
            String usernameError = "";

            if (!ConfigHelper.getRunMode().equals("dev")) {
                String userip = MetaHelper.GetCurrentIp(request);
                String useragent = request.getHeader("HTTP_USER_AGENT");
                qx_info qx = DBHelper.get_yzMessage(person.getLoginName(), ypassword, userip, useragent);
                if (qx != null) {
                    usernameSuccess = qx.getUsernameSuccess();
                    passwordError = qx.getPasswordError();
                    usernameError = qx.getUsernameError();
                }

                if (passwordError != null && passwordError != "") {
                    success = false;
                    //text = "登录密码字数不能小于8个字母,必须包含数字和字母,符号！";
                    text = passwordError;
                    person = null;

                    ajaxResult = AjaxResult.error(text);
                    return ajaxResult;
                } else if (usernameError != null && usernameError != "") {
                    ajaxResult = AjaxResult.error(usernameError);
                    return ajaxResult;
                }
            }

            Boolean uflag = false;
            uflag = WpServiceHelper.UpdatePassword(person.getId(), defaultpwd);
            if (uflag == false) {
                ajaxResult = AjaxResult.error("操作失败，修改用户密码失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success("修改用户密码成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

}
