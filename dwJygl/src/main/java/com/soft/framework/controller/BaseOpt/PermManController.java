package com.soft.framework.controller.BaseOpt;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.config.BaseConfig;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.yyszc.extend.DataTable;
import com.yyszc.wpbase.entity.Permission;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Controller
@Configuration
@RequestMapping(value = "/Service/BaseOpt/PermMan")
@Api(tags = "基本框架接口->权限管理接口")
public class PermManController {
    private Boolean GatherParams2Obj(Map<String, String> params, Permission entity, StringBuilder msgstr) {
        try {
            if (!StringUtil.IsNull(params.get("PermissionNo"))) {
                entity.setPermissionNo(params.get("PermissionNo"));
            }
            if (!StringUtil.IsNull(params.get("OperationModule"))) {
                entity.setOperationModule(params.get("OperationModule"));
            }
            if (!StringUtil.IsNull(params.get("Operation"))) {
                entity.setOperation(params.get("Operation"));
            }
            if (!StringUtil.IsNull(params.get("Subsystem"))) {
                entity.setSubsystem(params.get("Subsystem"));
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("程序产生异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/AddPerm", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "AddPerm", notes = "新增权限接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult AddPerm(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            StringBuilder msgstr = new StringBuilder();

            Permission entity = new Permission();

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，获取传输参数失败！");
                return ajaxResult;
            }

            String pid = "";
            pid = WpServiceHelper.AddPermission(entity);
            if (pid == null || pid.equals("")) {
                ajaxResult = AjaxResult.error("操作失败，新增权限信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success(pid);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ModifyPerm", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ModifyPerm", notes = "修改权限接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult ModifyPerm(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            StringBuilder msgstr = new StringBuilder();

            String PermId = params.get("PermId");

            Permission entity = null;
            entity = WpServiceHelper.GetPermissionById(PermId);
            if (entity == null) {
                ajaxResult = AjaxResult.error("操作失败，获取权限信息失败！");
                return ajaxResult;
            }

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，参数传输失败！");
                return ajaxResult;
            }

            Boolean uflag = false;
            uflag = WpServiceHelper.UpdatePermission(entity);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，修改权限信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success(entity.getPermissionNo().toString());
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/DeletePerm", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "DeleteAS", notes = "删除权限接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult DeletePerm(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try {
            String PermId = request.getParameter("PermId");

            Boolean uflag = false;
            uflag = WpServiceHelper.DeletePermissionById(PermId);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，删除权限信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success("删除操作权限成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }


    private boolean GetUIQueryString(HttpServletRequest request, StringBuilder strsql, StringBuilder orderstr, StringBuilder msgstr) {
        try {
            String PermissionNo = request.getParameter("PermissionNo");
            String Operation = request.getParameter("Operation");
            String Subsystem = request.getParameter("Subsystem");

            strsql.append("select * from Permission where PermissionKind='" + ConfigHelper.getModuleId() + "'");
            if (!StringUtil.IsNullOrEmpty(PermissionNo)) {
                strsql.append(" and PermissionNo like '%" + PermissionNo + "%'");
            }
            if (!StringUtil.IsNullOrEmpty(Operation)) {
                strsql.append(" and Operation like '%" + Operation + "%'");
            }
            if (!StringUtil.IsNullOrEmpty(Subsystem)) {
                strsql.append(" and Subsystem like '%" + Subsystem + "%'");
            }

            if (BaseConfig.getExtVersion().equals("3.2")) {
                String sortf = request.getParameter("sort");
                String sortd = request.getParameter("dir");
                if (!StringUtil.IsNullOrEmpty(sortf) && !StringUtil.IsNullOrEmpty(sortd)) {
                    orderstr.append(" order by " + sortf + " " + sortd);
                }
            } else {
                String jsons = request.getParameter("sort");
                ToolHelper.DeserializeExtJsSortInfo(jsons, orderstr);
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("系统异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/GetPermList", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetPermList", notes = "获取权限列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetPermList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();

        //参数获取
        String tmpstr = "";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = StringUtil.IsNullOrEmpty(starts) ? 0 : Integer.parseInt(starts);
        int limit = StringUtil.IsNullOrEmpty(limits) ? 20 : Integer.parseInt(limits);

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        String strsql = "";
        int rcount = 0;

        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return ajaxResult;
        }

        try {
            String rcsql = DBHelper.ToRecordCountSql(basesql.toString());

            String rrval = "0";
            rrval = WpServiceHelper.GetRecordCount(rcsql);
            if (rrval == null || rrval.equals("")) {
                ajaxResult = AjaxResult.error("操作失败，获取数据条目信息失败！");
                return ajaxResult;
            }

            if (!rrval.equals("")) {
                rcount = Integer.parseInt(rrval);
            }

            int pageCount = (rcount / limit) + 1;
            int currpage = start / limit;
            strsql = sqlhelper.ToPageSql(basesql.toString(), orderstr.toString(), limit, currpage);

            List<Permission> list = null;
            list = WpServiceHelper.GetPermissionList(strsql);
            if (list == null) {
                ajaxResult = AjaxResult.error("操作失败，获取权限信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.extgrid(Permission.class, rcount, list);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/GetPermById", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetPermById", notes = "获取指定具有指定ID的权限接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetPermById(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        String PermId = request.getParameter("PermId");
        if (StringUtil.IsNullOrEmpty(PermId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            Permission entity = null;

            entity = WpServiceHelper.GetPermissionById(PermId);
            if (entity == null) {
                ajaxResult = AjaxResult.error("操作失败，获取权限信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.extform(Permission.class, "获取信息成功！", entity);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ExportExcel", produces = {"text/plain;charset=UTF-8"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ExportExcel", notes = "导出权限列表信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String ExportExcel(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return JSON.toJSON(ajaxResult).toString();
        }

        try {
            DataTable dt = null;

            dt = WpServiceHelper.GetDataTable(basesql + " " + orderstr);
            if (dt == null) {
                ajaxResult = AjaxResult.error("操作失败，获取权限信息失败！");
                return JSON.toJSON(ajaxResult).toString();
            }

            StringBuilder fname = new StringBuilder();
            StringBuilder retstr = new StringBuilder();
            List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<ToolHelper.ExportColumnMode>();
            cmlist.add(new ToolHelper.ExportColumnMode("PermissionNo", "权限编号", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("OperationModule", "权限模块", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("Operation", "权限名称", 40));
            cmlist.add(new ToolHelper.ExportColumnMode("SubSystem", "所属系统", 40));

            boolean retb = ToolHelper.ExportDS2XlsFile(dt, "操作权限别信息", cmlist, retstr, fname);
            if (retb) {
                ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
                cfs.success = true;
                cfs.text = "生成文件成功！";
                cfs.fname = fname.toString();
                cfs.data1 = ToolHelper.File2Bytes(ConfigHelper.getTempPath() + ConfigHelper.getfSepChar() + fname.toString());
                cfs.data2 = null;

                String jsonstr = JSON.toJSON(cfs).toString();
                return jsonstr;
            } else {
                ajaxResult = AjaxResult.error("导出文件异常!");
                return JSON.toJSON(ajaxResult).toString();
            }
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }
}
