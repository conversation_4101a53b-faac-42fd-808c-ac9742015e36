package com.soft.framework.controller.CPSoft;

import com.soft.framework.common.utils.date.DateUtils;
import com.soft.framework.common.utils.file.FileUtil;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.ConfigHelper;
import com.soft.framework.helper.ToolHelper;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Controller
@Configuration
@RequestMapping("/Service/CpSoft/SysLog" )
public class SysLogController {

    @RequestMapping("/GatherSysLog")
    @ResponseBody
    public AjaxResult GatherSysLog(@RequestParam Map<String, String> params)
    {
        AjaxResult ajaxResult = null;

        String sQryDate="";
        String eQryDate="";
        String sQryPatch="";

        sQryDate = params.get("sQryDate").toString();
        eQryDate = params.get("eQryDate").toString();
        sQryPatch = params.get("sQryPatch").toString();

        if(!StringUtil.IsNullOrEmpty(sQryDate)) {
            sQryDate=sQryDate.substring(0,10);
        }
        if(!StringUtil.IsNullOrEmpty(eQryDate)) {
            eQryDate=eQryDate.substring(0,10);
        }

        Date sDate = DateUtils.getNowDate();
        Date eDate = DateUtils.getNowDate();

        if(!StringUtil.IsNullOrEmpty(sQryDate)) {
            sDate=DateUtils.parseDate(sQryDate);
        }
        if(!StringUtil.IsNullOrEmpty(eQryDate)) {
            eDate=DateUtils.parseDate(eQryDate);
        }

        if (sDate.compareTo(eDate)>0)
        {
            ajaxResult=AjaxResult.error("查询日期限制有误!");
            return ajaxResult;
        }
        else
        {
            String LogPath = ConfigHelper.getSysLogPath();

            List<String> msglist = new ArrayList<String>();
            Date tmpdt = sDate;
            while (tmpdt.compareTo(eDate)<=0)
            {
                String fname = LogPath + ConfigHelper.getfSepChar() + DateUtils.parseDateToStr("yyyyMMdd",tmpdt)+ ".log";
                ToolHelper.ReadFileToStr(fname,msglist);
                tmpdt = DateUtils.addDays(tmpdt,1);
            }

            if(!sQryPatch.equals("")) {
                CharSequence cs=sQryPatch;
                msglist=msglist.stream().filter(ss->ss.contains((cs))).collect(Collectors.toList());
            }

            StringBuilder sb=new StringBuilder();
            for (String ls:msglist)
            {
                sb.append(ls + "\n");
            }

            ajaxResult=AjaxResult.success(sb.toString());
            return ajaxResult;
        }
    }

    @RequestMapping("/DeleteSysLog")
    @ResponseBody
    public AjaxResult DeleteSysLog(@RequestParam Map<String, String> params)
    {
        AjaxResult ajaxResult = null;

        String sQryDate="";
        String eQryDate="";

        sQryDate = params.get("sQryDate").toString();
        eQryDate = params.get("eQryDate").toString();

        if(!StringUtil.IsNullOrEmpty(sQryDate)) {
            sQryDate=sQryDate.substring(0,10);
        }
        if(!StringUtil.IsNullOrEmpty(eQryDate)) {
            eQryDate=eQryDate.substring(0,10);
        }

        Date sDate = DateUtils.getNowDate();
        Date eDate = DateUtils.getNowDate();

        if(!StringUtil.IsNullOrEmpty(sQryDate)) {
            sDate=DateUtils.parseDate(sQryDate);
        }
        if(!StringUtil.IsNullOrEmpty(eQryDate)) {
            eDate=DateUtils.parseDate(eQryDate);
        }

        if (sDate.compareTo(eDate)>0)
        {
            ajaxResult=AjaxResult.error("查询日期限制有误!");
            return ajaxResult;
        }
        else
        {
            String LogPath = ConfigHelper.getSysLogPath();

            Date tmpdt = sDate;
            while (tmpdt.compareTo(eDate)<=0)
            {
                String fname = LogPath + ConfigHelper.getfSepChar() + DateUtils.parseDateToStr("yyyyMMdd",tmpdt)+ ".log";
                FileUtil.Delete(fname);
                tmpdt = DateUtils.addDays(tmpdt,1);
            }

            ajaxResult=AjaxResult.success("删除日志成功！");
            return ajaxResult;
        }
    }
}
