package com.soft.framework.controller.CPSoft;

import com.soft.framework.common.utils.security.Md5Util;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@Controller
@Configuration
@RequestMapping("/Service/Cpsoft/CmEncoder")
public class CMEncoderController {

    @PostMapping("/ExecuteEncoder")
    @ResponseBody
    public AjaxResult ExecuteEncoder(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        String SrcCode = request.getParameter("SrcCode");
        if (StringUtil.IsNullOrEmpty(SrcCode)) {
            ajaxResult =AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            String MD5Password = Md5Util.getMD5Str(SrcCode);
            ajaxResult = AjaxResult.success(MD5Password);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
}
