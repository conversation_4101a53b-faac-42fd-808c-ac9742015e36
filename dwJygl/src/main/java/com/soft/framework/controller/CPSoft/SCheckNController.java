package com.soft.framework.controller.CPSoft;

import com.alibaba.fastjson.JSON;
import com.yyszc.extend.DataTable;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.SqlHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@Controller
@Configuration
@RequestMapping("/Service/CpSoft/SCheckN" )
@SuppressWarnings("unchecked")
public class SCheckNController {
    private static final Logger sysLog = LoggerFactory.getLogger(SCheckNController.class);

    @RequestMapping(value="/ExecuteCheck", produces={"text/plain;charset=UTF-8"})
    @ResponseBody
    public String ExecuteCheck(HttpServletRequest request)
    {
        AjaxResult ajaxResult = null;
        SqlHelper sqlhelper=new SqlHelper();

        String jsonstr="";
        try
        {
            String strTable = request.getParameter("table");
            String strCondition = request.getParameter("condtion");
            String strFilters = request.getParameter("filter");
            String strPar = request.getParameter("params");
            String strQryColumn = request.getParameter("columns");
            String strOrderBy = request.getParameter("order");
            String strGroup = request.getParameter("group");
            String strDistF = request.getParameter("distf");

            if (StringUtil.isNotEmpty(strCondition)) {
                strCondition=strCondition.replace("opera-equal","=")
                        .replace("opera-less","<")
                        .replace("opera-large",">")
                        .replace(" opera-andg "," and ")
                        .replace(" opera-org "," or ");
                strCondition = strCondition.replace("(*)", "%");
            }
            String[] arrColumns = strQryColumn.split(",");

            StringBuilder sbSql = new StringBuilder("select ");
            if (!strQryColumn.equals(""))
            {
                String columns = "";
                for (int idx = 0; idx < arrColumns.length; idx++)
                {
                    if (!columns.equals("")) {
                        columns += "," + arrColumns[idx];
                    } else {
                        columns += arrColumns[idx];
                    }
                }
                sbSql.append(columns);
            }
            sbSql.append(" From " + strTable+" Where 1=1 ");

            if (!strCondition.equals(""))
            {
                sbSql.append(" and " + strCondition);
            }
            if (!strFilters.equals("") && !strPar.equals(""))
            {
                sbSql.append(" and ( ");
                String[] arrFilter = strFilters.split(",");
                String[] arrPars = strPar.split(",");

                String filters = "";
                String _filters = "";
                for (int iddx = 0; iddx < arrFilter.length; iddx++)
                {
                    _filters = "(";
                    String strFilter = arrFilter[iddx];

                    for (int iddy = 0; iddy < arrPars.length; iddy++)
                    {
                        String strParam = arrPars[iddy];
                        _filters += " upper(" + strFilter + ") like '%" + strParam + "%' ";
                        if (iddy < arrPars.length - 1) {
                            _filters += " and ";
                        }
                    }

                    _filters += ")";

                    if (filters != "") {
                        filters += " or " + _filters;
                    } else {
                        filters = _filters;
                    }
                }

                sbSql.append(filters);
                sbSql.append(") ");
            }
            if (!strGroup.equals("")) {
                sbSql = sbSql.append(" " + strGroup);
            }

            String orderstr = " order by " + strOrderBy;
            int rcount = 0;
            String tmpstr = "";
            String basesql = sbSql.toString();

            String strsql = basesql+" "+orderstr;
            if (StringUtil.isNotEmpty(strDistF))
            {
                strsql=strsql.replaceFirst("select ","select "+strDistF+" ");
            }
            DataTable dt = sqlhelper.GetDataTable(strsql);
            jsonstr= AjaxResult.extgrid(dt);
            return jsonstr;
        }
        catch (Exception Ex)
        {
            ajaxResult= AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }
}
