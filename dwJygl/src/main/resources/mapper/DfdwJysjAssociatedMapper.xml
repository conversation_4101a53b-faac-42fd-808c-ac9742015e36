<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.jygl.mapper.DfdwJysjAssociatedMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.jygl.entity.DfdwJysjAssociated">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="associatedUnits" column="associated_units" jdbcType="VARCHAR"/>
            <result property="uscc" column="USCC" jdbcType="VARCHAR"/>
            <result property="contactPerson" column="contact_person" jdbcType="VARCHAR"/>
            <result property="contactPersonNumber" column="contact_person_number" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,associated_units,USCC,
        contact_person,contact_person_number,email
    </sql>
    <insert id="batchInsert">
        INSERT INTO DFDW_JYSJ_Associated (associated_units, USCC, contact_person, contact_person_number, email)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.associatedUnits}, #{item.uscc}, #{item.contactPerson}, #{item.contactPersonNumber}, #{item.email})
        </foreach>
    </insert>
</mapper>
