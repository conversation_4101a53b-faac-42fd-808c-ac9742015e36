<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.common.dfdw_dict.mapper.DfdwTDictDataMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.common.dfdw_dict.entity.DfdwTDictData">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="dictType" column="dict_type" jdbcType="VARCHAR"/>
        <result property="label" column="label" jdbcType="VARCHAR"/>
        <result property="value" column="value" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="VARCHAR"/>
    </resultMap>


    <sql id="Base_Column_List">
        id,dict_type,label,
        value,sort,status,
        remark,creator,create_time,
        updater,update_time,deleted
    </sql>
</mapper>
