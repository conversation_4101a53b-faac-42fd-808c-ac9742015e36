<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.jygl.mapper.DfdwJysjCollectionMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.jygl.entity.DfdwJysjCollection">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="numberOfDunningPeriods" column="number_of_dunning_periods" jdbcType="VARCHAR"/>
            <result property="collectionDate" column="collection_date" jdbcType="TIMESTAMP"/>
            <result property="lastDunningDate" column="last_dunning_date" jdbcType="TIMESTAMP"/>
            <result property="state" column="state" jdbcType="BIGINT"/>
            <result property="emailStatus" column="email_status" jdbcType="INTEGER"/>
            <result property="confirmationStatus" column="confirmation_status" jdbcType="INTEGER"/>
            <result property="businessDataId" column="business_data_id" jdbcType="BIGINT"/>
            <result property="annexId" column="annex_id" jdbcType="VARCHAR"/>
            <result property="annexType" column="annex_type" jdbcType="INTEGER"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="supportDocsIds" column="support_docs_ids" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="createBy" column="update_by" jdbcType="VARCHAR"/>

    </resultMap>

    <sql id="Base_Column_List">
        id,number_of_dunning_periods,collection_date,
        last_dunning_date,state,email_status,
        confirmation_status,business_data_id,annex_id
    </sql>
    <select id="pageList" resultMap="BaseResultMap">
        select t.*, t1.company_name as companyName ,t1.project_name as projectName,t1.unit,
        <if test="query.type == 1">
            t1.transfer_time as transferTime
        </if>
        <if test="query.type == 2">
            t1.project_completed_date as transferTime
        </if>

        from DFDW_JYSJ_Collection t
        <if test="query.type == 1">
            LEFT JOIN DFDW_JYSJ_Business_data t1 on t1.id = t.business_data_id
        </if>
        <if test="query.type == 2">
            LEFT JOIN DFDW_JYSJ_DebtManagement t1 on t1.id = t.business_data_id
        </if>
        <where>
            t.type = #{query.type}
            <if test="query.id != null">
                AND t.id = #{query.id}
            </if>
            <if test="query.companyName != null and query.companyName != ''">
                AND t1.company_name LIKE CONCAT('%', #{query.companyName}, '%')
            </if>
            <if test="query.unit != null and query.unit != ''">
                AND t1.unit LIKE CONCAT('%', #{query.unit}, '%')
            </if>
            <if test="query.projectName != null and query.projectName != ''">
                AND t1.project_name LIKE CONCAT('%', #{query.projectName}, '%')
            </if>
            <if test="query.topGroupId != null">
                AND t1.topGroupId = #{query.topGroupId}
            </if>
            <if test="query.state != null">
                AND t.state = #{query.state}
            </if>
            <if test="query.emailStatus != null">
                AND t.email_status = #{query.emailStatus}
            </if>
            <if test="query.confirmationStatus != null">
                AND t.confirmation_status = #{query.confirmationStatus}
            </if>
        </where>
        order by t.id desc
    </select>

    <select id="pageList1" resultMap="BaseResultMap">
        select t.*, t1.company_name as companyName ,t1.project_name as projectName,t1.project_completed_date as transferTime
        from DFDW_JYSJ_Collection t
        LEFT JOIN DFDW_JYSJ_DebtManagement t1 on t1.id = t.business_data_id
        <where>
            t.type = 2
            <if test="query.id != null">
                AND t.id = #{query.id}
            </if>
            <if test="query.companyName != null and query.companyName != ''">
                AND t1.company_name LIKE CONCAT('%', #{query.companyName}, '%')
            </if>
            <if test="query.projectName != null and query.projectName != ''">
                AND t1.project_name LIKE CONCAT('%', #{query.projectName}, '%')
            </if>
            <if test="query.topGroupId != null">
                AND t1.company_id = #{query.topGroupId}
            </if>
            <if test="query.state != null">
                AND t.state = #{query.state}
            </if>
            <if test="query.emailStatus != null">
                AND t.email_status = #{query.emailStatus}
            </if>
            <if test="query.confirmationStatus != null">
                AND t.confirmation_status = #{query.confirmationStatus}
            </if>
        </where>
        order by t.id desc
    </select>
</mapper>
