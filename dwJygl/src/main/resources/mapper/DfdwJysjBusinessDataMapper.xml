<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.jygl.mapper.DfdwJysjBusinessDataMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.jygl.entity.DfdwJysjBusinessData">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
            <result property="unit" column="unit" jdbcType="VARCHAR"/>
            <result property="subjectNumber" column="subject_number" jdbcType="VARCHAR"/>
            <result property="subjectName" column="subject_name" jdbcType="VARCHAR"/>
            <result property="customerFileNumber" column="customer_file_number" jdbcType="INTEGER"/>
            <result property="customer" column="customer" jdbcType="VARCHAR"/>
            <result property="projectTypeCode" column="project_type_code" jdbcType="VARCHAR"/>
            <result property="projectTypeName" column="project_type_name" jdbcType="VARCHAR"/>
            <result property="projectCode" column="project_code" jdbcType="VARCHAR"/>
            <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
            <result property="transferVoucher" column="transfer_voucher" jdbcType="VARCHAR"/>
            <result property="money" column="money" jdbcType="DECIMAL"/>
            <result property="transferTime" column="transfer_time" jdbcType="TIMESTAMP"/>
            <result property="agingOfAccounts" column="aging_of_accounts" jdbcType="VARCHAR"/>
            <result property="agingBuckets" column="aging_buckets" jdbcType="VARCHAR"/>
            <result property="fundingSource" column="funding_source" jdbcType="VARCHAR"/>
            <result property="businessPartnerClassification" column="business_partner_classification" jdbcType="VARCHAR"/>
            <result property="addTime" column="add_time" jdbcType="TIMESTAMP"/>
            <result property="removeTime" column="remove_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="topGroupId" column="topGroupId" jdbcType="INTEGER"/>
            <result property="collectionCounts" column="collection_counts" jdbcType="VARCHAR"/>
            <result property="recoveredAmount" column="recovered_amount" jdbcType="DECIMAL"/>
            <result property="recoveredTime" column="recovered_time" jdbcType="TIMESTAMP"/>
            <result property="lastCollectionTime" column="last_collection_time" jdbcType="TIMESTAMP"/>
            <result property="isInLawsuit" column="is_inLawsuit" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="enable" column="enable" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,company_name,unit,
        subject_number,subject_name,customer_file_number,
        customer,project_type_code,project_type_name,
        project_code,project_name,transfer_voucher,
        money,transfer_time,aging_of_accounts,
        aging_buckets,funding_source,business_partner_classification,
        add_time,remove_time,remark,collection_counts,
        last_collection_time,create_time,create_by,enable,
        update_time,update_by
    </sql>



    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO dfdw_jysj_business_data (
            company_name,unit, subject_number, subject_name, customer_file_number, customer,
            project_type_code, project_type_name, project_code, project_name, transfer_voucher,
            money, transfer_time, aging_of_accounts, aging_buckets, funding_source,
            business_partner_classification, add_time, remove_time, remark,topGroupId,status
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.companyName, jdbcType=VARCHAR},
                #{item.unit, jdbcType=VARCHAR},
                #{item.subjectNumber, jdbcType=VARCHAR},
                #{item.subjectName, jdbcType=VARCHAR},
                #{item.customerFileNumber, jdbcType=VARCHAR},
                #{item.customer, jdbcType=VARCHAR},
                #{item.projectTypeCode, jdbcType=VARCHAR},
                #{item.projectTypeName, jdbcType=VARCHAR},
                #{item.projectCode, jdbcType=VARCHAR},
                #{item.projectName, jdbcType=VARCHAR},
                #{item.transferVoucher, jdbcType=VARCHAR},
                #{item.money, jdbcType=DECIMAL},
                #{item.transferTime, jdbcType=TIMESTAMP},
                #{item.agingOfAccounts, jdbcType=VARCHAR},
                #{item.agingBuckets, jdbcType=VARCHAR},
                #{item.fundingSource, jdbcType=VARCHAR},
                #{item.businessPartnerClassification, jdbcType=VARCHAR},
                #{item.addTime, jdbcType=TIMESTAMP},
                #{item.removeTime, jdbcType=TIMESTAMP},
                #{item.remark, jdbcType=VARCHAR},
                #{item.topGroupId, jdbcType=INTEGER},
                0
            )
        </foreach>
    </insert>

    <select id="getSummary" resultType="com.soft.gcc.xtbg.jygl.dto.AccountsReceivableDto">

        -- 行转列统计SQL（unit改为读取GroupItem的Groupname字段）
        WITH AllUnits AS (
        SELECT DISTINCT
        ISNULL(bd.company_name, 'Unknown Company') AS company_name,
        CASE
        WHEN bd.topGroupId IS NULL THEN '其他'
        ELSE ISNULL(gi.groupname, '其他')
        END AS unit,
        bd.topGroupId
        FROM [dbo].[DFDW_JYSJ_Business_data] bd
        LEFT JOIN [dbo].[GroupItem] gi ON bd.topGroupId = gi.id
        WHERE bd.status = 0
        <if test="deptId != null">
            AND bd.topGroupId = #{deptId}
        </if>
        )
        SELECT
        au.company_name AS companyName,
        au.unit AS unit,
        CASE
        WHEN au.topGroupId IS NULL THEN ********
        ELSE ISNULL(gi.XH, ********)
        END AS sortOrder,
        -- 6个月以内
        ISNULL(SUM(CASE WHEN DATEDIFF(DAY, bd.transfer_time, GETDATE()) &lt;= 182 THEN 1 END), 0) AS sixMonCount,
        CAST(ROUND(ISNULL(SUM(CASE WHEN DATEDIFF(DAY, bd.transfer_time, GETDATE()) &lt;= 182 THEN bd.money END), 0), 2) AS DECIMAL(18,2)) AS sixMonAmount,
        0 AS sixMonLedger,
        -- 1年以内
        ISNULL(SUM(CASE WHEN DATEDIFF(DAY, bd.transfer_time, GETDATE()) > 182 AND DATEDIFF(DAY, bd.transfer_time, GETDATE()) &lt;= 365 THEN 1 END), 0) AS oneYearCount,
        CAST(ROUND(ISNULL(SUM(CASE WHEN DATEDIFF(DAY, bd.transfer_time, GETDATE()) > 182 AND DATEDIFF(DAY, bd.transfer_time, GETDATE()) &lt;= 365 THEN bd.money END), 0), 2) AS DECIMAL(18,2)) AS oneYearAmount,
        0 AS oneYearLedger,
        -- 1-2年
        ISNULL(SUM(CASE WHEN DATEDIFF(DAY, bd.transfer_time, GETDATE()) > 365 AND DATEDIFF(DAY, bd.transfer_time, GETDATE()) &lt;= 730 THEN 1 END), 0) AS oneToTwoCount,
        CAST(ROUND(ISNULL(SUM(CASE WHEN DATEDIFF(DAY, bd.transfer_time, GETDATE()) > 365 AND DATEDIFF(DAY, bd.transfer_time, GETDATE()) &lt;= 730 THEN bd.money END), 0), 2) AS DECIMAL(18,2)) AS oneToTwoAmount,
        0 AS oneToTwoLedger,
        -- 2-3年
        ISNULL(SUM(CASE WHEN DATEDIFF(DAY, bd.transfer_time, GETDATE()) > 730 AND DATEDIFF(DAY, bd.transfer_time, GETDATE()) &lt;= 1095 THEN 1 END), 0) AS twoToThreeCount,
        CAST(ROUND(ISNULL(SUM(CASE WHEN DATEDIFF(DAY, bd.transfer_time, GETDATE()) > 730 AND DATEDIFF(DAY, bd.transfer_time, GETDATE()) &lt;= 1095 THEN bd.money END), 0), 2) AS DECIMAL(18,2)) AS twoToThreeAmount,
        0 AS twoToThreeLedger,
        -- 3年以上
        ISNULL(SUM(CASE WHEN DATEDIFF(DAY, bd.transfer_time, GETDATE()) > 1095 THEN 1 END), 0) AS threeAboveCount,
        CAST(ROUND(ISNULL(SUM(CASE WHEN DATEDIFF(DAY, bd.transfer_time, GETDATE()) > 1095 THEN bd.money END), 0), 2) AS DECIMAL(18,2)) AS threeAboveAmount,
        0 AS threeAboveLedger
        FROM AllUnits au
        LEFT JOIN [dbo].[DFDW_JYSJ_Business_data] bd
        ON au.company_name = ISNULL(bd.company_name, 'Unknown Company')
        AND (
        (au.topGroupId IS NULL AND bd.topGroupId IS NULL) OR
        (au.topGroupId IS NOT NULL AND au.topGroupId = bd.topGroupId)
        )
        AND bd.transfer_time IS NOT NULL
        AND bd.status = 0
        LEFT JOIN [dbo].[GroupItem] gi
        ON au.topGroupId = gi.id
        GROUP BY
        au.company_name,
        au.unit,
        au.topGroupId,
        gi.XH
        ORDER BY
        CASE
        WHEN au.topGroupId IS NULL THEN ********
        ELSE ISNULL(gi.XH, ********)
        END ASC,
        au.company_name,
        au.unit;


    </select>




    <select id="getProjectNameAndCode" resultType="com.soft.gcc.xtbg.jygl.dto.ProjectNameAndCodeDto">
        SELECT DISTINCT
            project_code AS projectCode,
            project_name AS projectName
        FROM [dbo].[DFDW_JYSJ_Business_data]
        WHERE project_code IS NOT NULL
          AND project_name IS NOT NULL
          AND project_code != ''
          AND project_name != ''
        ORDER BY project_code;
    </select>



    <select id="getCompanyOptions" resultType="java.lang.String">
        select distinct company_name from DFDW_JYSJ_Business_data
        <where>
            <if test="allAdmin!= null and allAdmin == false">
                and topGroupId = #{topGroupId}
            </if>
        </where>
    </select>




</mapper>
