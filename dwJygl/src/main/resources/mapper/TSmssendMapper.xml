<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.common.t_smssend.mapper.TSmssendMapper">




    <insert id="sendSms">
        INSERT INTO T_SMSSEND (Source,Phone,DateTime,Sequence,Result,Info)
        SELECT
            'AQGL',
            #{phone},
            getdate(),
            isnull(MAX ( sequence ), 0) + 1,
            0,
            #{content}
        FROM
            [T_SMSSend]
        WHERE
            Source = 'AQGL'
          AND Phone = #{phone}
          AND DateTime = getdate()
    </insert>

</mapper>
