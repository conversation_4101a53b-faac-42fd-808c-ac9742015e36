<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.common.lc_workFlow.mapper.LcWorkflowMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.common.lc_workFlow.entity.LcWorkflow">
            <id property="id" column="ID" jdbcType="INTEGER"/>
            <result property="lcDefineid" column="lc_defineID" jdbcType="INTEGER"/>
            <result property="ywid" column="ywID" jdbcType="INTEGER"/>
            <result property="lcJdid" column="lc_jdID" jdbcType="INTEGER"/>
            <result property="lcJdmc" column="lc_jdmc" jdbcType="VARCHAR"/>
            <result property="groupid" column="groupID" jdbcType="INTEGER"/>
            <result property="groupname" column="groupName" jdbcType="VARCHAR"/>
            <result property="personzgh" column="personZgh" jdbcType="VARCHAR"/>
            <result property="personname" column="personName" jdbcType="VARCHAR"/>
            <result property="transdate" column="transdate" jdbcType="TIMESTAMP"/>
            <result property="feed" column="feed" jdbcType="VARCHAR"/>
            <result property="number" column="number" jdbcType="INTEGER"/>
            <result property="bxtype" column="BXType" jdbcType="VARCHAR"/>
            <result property="pno" column="PNO" jdbcType="VARCHAR"/>
            <result property="startdate" column="startdate" jdbcType="TIMESTAMP"/>
            <result property="lcbyrole" column="LcByRole" jdbcType="INTEGER"/>
            <result property="isback" column="isback" jdbcType="INTEGER"/>
            <result property="useback" column="useback" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,lc_defineID,ywID,
        lc_jdID,lc_jdmc,groupID,
        groupName,personZgh,personName,
        transdate,feed,number,
        BXType,PNO,startdate,
        LcByRole,isback,useback
    </sql>
</mapper>
