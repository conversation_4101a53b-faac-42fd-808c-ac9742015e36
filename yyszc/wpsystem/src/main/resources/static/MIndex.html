<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>集团数字平台-免登入口</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <link rel="stylesheet" type="text/css" href="JSControl/ExtJs/ExtResources/css/ext-all.css" />
    <link rel="stylesheet" type="text/css" href="JSControl/ExtJs/ext_custom.css" />
    <script type="text/javascript" src="JSControl/ExtJs/ext-base.js"></script>
    <script type="text/javascript" src="JSControl/ExtJs/ext-all.js"></script>
    <script type="text/javascript" src="JSControl/ExtJs/ext-lang-zh_CN.js"></script>
    <script type="text/javascript">
        var _framemark="wpframe";
        var _sysmark="wpframe";
        var _mid="";
        var _token="";

        function QueryString(lname){
            var name,
                value,
                i;
            var str = location.href;
            var num = str.indexOf("?")
            str = str.substr(num + 1);
            var arrtmp = str.split("&");
            for(i = 0; i < arrtmp.length; i ++ ){
                num = arrtmp[i].indexOf("=");
                if(num > 0){
                    name = arrtmp[i].substring(0, num);
                    value = arrtmp[i].substr(num + 1);
                    if(name == lname)
                        return value;
                }
            }
            return "";
        }

        function CloseWindow(window) {
            try {
                var browserName = navigator.appName;
                if (browserName == "Netscape")
                {
                    window.location.href = "about:blank";
                    window.close();
                } else if (browserName == "Microsoft Internet Explorer")
                {
                    window.opener = null;
                    window.close();
                }
            } catch (e) {

            }
        }

        function MLogin(suncf,failf) {
            //alert('in MLogin');
            if(_framemark==""||_framemark==null||_framemark==undefined){
                _framemark='wpframe';
            }
            //alert(_framemark);
            //alert(_mid);

            var storage = window.sessionStorage;
            //alert(storage);
            if(storage!=undefined)
            {
                _token = storage.getItem(_framemark+"_token");
            }
            //alert(_token);
            if(_token==""||_token=="undefined")
            {
                Ext.MessageBox.alert("操作提示","传输接口参数有误！");
                return;
            }

            Ext.Ajax.defaultHeaders={
                Authorization: _token
            };

            //alert("token="+_token);
            Ext.Ajax.request({
                url: "./Service/Base/User/MLogin",
                success: function(res) {
                    var robj = Ext.decode(res.responseText);
                    if (robj.success == true||robj.success == "true") {
                        loginToken = robj.token;
                        cpsVersion = robj.cpsversion;
                        _sysmark = robj.sysmark;
                        var storage = window.sessionStorage;
                        if(storage!=undefined)
                        {
                            storage.setItem(_sysmark+"_xsystem","true");
                            storage.setItem(_sysmark+"_token",loginToken);
                            storage.setItem(_sysmark+"_cpsversion",cpsVersion);
                        }
                        if(suncf) suncf();
                    }else if(robj.text!=undefined){
                        Ext.MessageBox.alert("操作提示",robj.text);
                        if(failf) failf();
                    }
                },
                failure: function(res) {
                    if(failf) failf();
                }
            });
        }

        function MLoginProx()
        {
            _framemark=QueryString("sm");
            _mid=QueryString("mid");
            //alert(_mid);

            MLogin(function(){
                var storage = window.sessionStorage;
                if(storage!=undefined)
                {
                    window.location.href = "Page/Frame/Frame.html?mid="+_mid+"&sm="+_framemark+"&v="+cpsVersion;
                }else {
                    Ext.MessageBox.alert("操作提示","登录系统失败，程序将退出",function(){
                        CloseWindow(window);
                    });
                }
            },function(){
                Ext.MessageBox.alert("操作提示","登录系统失败，程序将退出",function(){
                    CloseWindow(window);
                });
            });
        }

        Ext.onReady(function () {
            MLoginProx();
        });
    </script>
</head>
<body>
</body>
</html>
