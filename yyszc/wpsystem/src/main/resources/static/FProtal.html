<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>Title</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <script type="text/javascript" language="javascript">
        window.onload = function () {
            var retf = confirm("当前Session已经过期，确认返回登录窗口?");
            LocationToLogin(retf);
        }
        function CloseWindow(window) {
            try {
                window.opener = window;
                var win = window.open("", "_self");
                win.close();
                //frame的时候
                top.close();
            } catch (e) {
            }
        }
        function LocationToLogin(flag) {
            var topw = window.top;
            var openw = window.opener;
            if (openw) {
                topw.window.close();
                var topopenw = openw.top;
                if (flag) {
                    if (topopenw) {
                        topopenw.location.href = "Index.html";
                    } else {
                        openw.location = "Index.html";
                    }
                }else{
                    if (topopenw) topopenw.CloseWindow(topopenw);
                    else openw.CloseWindow(openw);
                }

            } else if (topw) {
                if (flag) {
                    topw.location.href = "MIndex.html";
                } else {
                    CloseWindow(topw);
                }
            } else {
                if (flag) {
                    window.location.href = "MIndex.html";
                } else {
                    CloseWindow(self);
                }
            }
        }
    </script>
</head>
<body>

</body>
</html>