<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <link rel="stylesheet" href="./Vue/Lib/element-ui-plus.css" />
    <script src="./Vue/Lib/vue.global.js"></script>
    <script src="./Vue/Lib/vue3-sfc-loader.js"></script>
    <script src="./Vue/Lib/element-ui-plus.js"></script>
</head>
<body>
<div id="container"></div>
<script>
    const options = {
        moduleCache: {
            vue: Vue
        },

        async getFile(url) {
            const res = await fetch(url);
            if ( !res.ok )
                throw Object.assign(new Error(url+' '+res.statusText), { res });
            return await res.text();
        },

        addStyle(textContent) {
            const style = Object.assign(document.createElement('style'), { textContent });
            const ref = document.head.getElementsByTagName('style')[0] || null;
            document.head.insertBefore(style, ref);
        }
    }

    const { loadModule } = window['vue3-sfc-loader'];
    const app = Vue.createApp(Vue.defineAsyncComponent(() => loadModule('./Vue/Component/Demo/main.vue', options)));
    app.use(ElementPlus);
    app.mount('#container');
</script>
</body>
</html>