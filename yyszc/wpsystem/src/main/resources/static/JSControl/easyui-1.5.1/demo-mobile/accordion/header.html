<!doctype html>
<html>
<head>
    <meta charset="UTF-8">  
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Custom Accordion Header - jQuery EasyUI Mobile Demo</title>  
    <link rel="stylesheet" type="text/css" href="../../themes/metro/easyui.css">  
    <link rel="stylesheet" type="text/css" href="../../themes/mobile.css">  
    <link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
    <script type="text/javascript" src="../../jquery.min.js"></script>  
    <script type="text/javascript" src="../../jquery.easyui.min.js"></script> 
    <script type="text/javascript" src="../../jquery.easyui.mobile.js"></script> 
</head>
<body>
	<div class="easyui-navpanel">
		<header>
			<div class="m-toolbar">
				<span class="m-title">Custom Accordion Header</span>
			</div>
		</header>
		<div class="easyui-accordion" data-options="fit:true,border:false,selected:-1">
			<div>
				<header>
					<div class="hh-inner">
						<span>List</span>
						<span class="m-badge" style="float:right">26/51</span>
					</div>
				</header>
				<ul class="m-list">
					<li>WLAN</li>
					<li>Memory</li>
					<li>Screen</li>
					<li>More...</li>
				</ul>
			</div>
			<div href="_content.html" style="padding:10px">
				<header>
					<div class="hh-inner">
						<span>Ajax</span>
						<span style="float:right">
							<span style="color:#999;margin-right:5px">Loading via ajax</span>
							<span class="m-badge">23</span>
						</span>
					</div>
				</header>
			</div>
		</div>
	</div>
	<style scoped>
		.hh-inner{
			position: relative;
			line-height: 20px;
			background: #fff;
			font-weight: bold;
			margin: -5px;
			padding: 5px;
		}
	</style>
</body>	
</html>
