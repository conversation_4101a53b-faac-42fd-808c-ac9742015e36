<!doctype html>
<html>
<head>
    <meta charset="UTF-8">  
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Button Style - jQuery EasyUI Mobile Demo</title>  
    <link rel="stylesheet" type="text/css" href="../../themes/metro/easyui.css">  
    <link rel="stylesheet" type="text/css" href="../../themes/mobile.css">  
    <link rel="stylesheet" type="text/css" href="../../themes/color.css">  
    <link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
    <script type="text/javascript" src="../../jquery.min.js"></script>  
    <script type="text/javascript" src="../../jquery.easyui.min.js"></script>
    <script type="text/javascript" src="../../jquery.easyui.mobile.js"></script>
</head>
<body>
	<div class="easyui-navpanel">
		<header>
			<div class="m-toolbar">
				<span class="m-title">Button Style</span>
			</div>
		</header>
		<div style="padding:20px">
			<p>Style</p>
			<a href="#" class="easyui-linkbutton" style="width:80px">Normal</a>
			<a href="#" class="easyui-linkbutton" plain="true" outline="true" style="width:80px">Outline</a>
			<a href="#" class="easyui-linkbutton" disabled style="width:80px">Disabled</a>

			<p>Colors<p>
			<p><a href="#" class="easyui-linkbutton c1" style="width:100%">Button1</a></p>
			<p><a href="#" class="easyui-linkbutton c2" style="width:100%">Button2</a></p>
			<p><a href="#" class="easyui-linkbutton c3" style="width:100%">Button3</a></p>
			<p><a href="#" class="easyui-linkbutton c4" style="width:100%">Button4</a></p>
			<p><a href="#" class="easyui-linkbutton c5" style="width:100%">Button5</a></p>
			<p><a href="#" class="easyui-linkbutton c6" style="width:100%">Button6</a></p>
		</div>
	</div>
</body>	
</html>
