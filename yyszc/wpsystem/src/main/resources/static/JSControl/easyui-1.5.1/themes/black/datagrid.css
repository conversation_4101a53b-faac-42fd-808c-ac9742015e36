.datagrid .panel-body {
  overflow: hidden;
  position: relative;
}
.datagrid-view {
  position: relative;
  overflow: hidden;
}
.datagrid-view1,
.datagrid-view2 {
  position: absolute;
  overflow: hidden;
  top: 0;
}
.datagrid-view1 {
  left: 0;
}
.datagrid-view2 {
  right: 0;
}
.datagrid-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.3;
  filter: alpha(opacity=30);
  display: none;
}
.datagrid-mask-msg {
  position: absolute;
  top: 50%;
  margin-top: -20px;
  padding: 10px 5px 10px 30px;
  width: auto;
  height: 16px;
  border-width: 2px;
  border-style: solid;
  display: none;
}
.datagrid-empty {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 25px;
  line-height: 25px;
  text-align: center;
}
.datagrid-sort-icon {
  padding: 0;
  display: none;
}
.datagrid-toolbar {
  height: auto;
  padding: 1px 2px;
  border-width: 0 0 1px 0;
  border-style: solid;
}
.datagrid-btn-separator {
  float: left;
  height: 24px;
  border-left: 1px solid #444;
  border-right: 1px solid #777;
  margin: 2px 1px;
}
.datagrid .datagrid-pager {
  display: block;
  margin: 0;
  border-width: 1px 0 0 0;
  border-style: solid;
}
.datagrid .datagrid-pager-top {
  border-width: 0 0 1px 0;
}
.datagrid-header {
  overflow: hidden;
  cursor: default;
  border-width: 0 0 1px 0;
  border-style: solid;
}
.datagrid-header-inner {
  float: left;
  width: 10000px;
}
.datagrid-header-row,
.datagrid-row {
  height: 32px;
}
.datagrid-header td,
.datagrid-body td,
.datagrid-footer td {
  border-width: 0 1px 1px 0;
  border-style: dotted;
  margin: 0;
  padding: 0;
}
.datagrid-cell,
.datagrid-cell-group,
.datagrid-header-rownumber,
.datagrid-cell-rownumber {
  margin: 0;
  padding: 0 4px;
  white-space: nowrap;
  word-wrap: normal;
  overflow: hidden;
  height: 18px;
  line-height: 18px;
  font-size: 14px;
}
.datagrid-header .datagrid-cell {
  height: auto;
}
.datagrid-header .datagrid-cell span {
  font-size: 14px;
}
.datagrid-cell-group {
  text-align: center;
  text-overflow: ellipsis;
}
.datagrid-header-rownumber,
.datagrid-cell-rownumber {
  width: 30px;
  text-align: center;
  margin: 0;
  padding: 0;
}
.datagrid-body {
  margin: 0;
  padding: 0;
  overflow: auto;
  zoom: 1;
}
.datagrid-view1 .datagrid-body-inner {
  padding-bottom: 20px;
}
.datagrid-view1 .datagrid-body {
  overflow: hidden;
}
.datagrid-footer {
  overflow: hidden;
}
.datagrid-footer-inner {
  border-width: 1px 0 0 0;
  border-style: solid;
  width: 10000px;
  float: left;
}
.datagrid-row-editing .datagrid-cell {
  height: auto;
}
.datagrid-header-check,
.datagrid-cell-check {
  padding: 0;
  width: 27px;
  height: 18px;
  font-size: 1px;
  text-align: center;
  overflow: hidden;
}
.datagrid-header-check input,
.datagrid-cell-check input {
  margin: 0;
  padding: 0;
  width: 15px;
  height: 18px;
}
.datagrid-resize-proxy {
  position: absolute;
  width: 1px;
  height: 10000px;
  top: 0;
  cursor: e-resize;
  display: none;
}
.datagrid-body .datagrid-editable {
  margin: 0;
  padding: 0;
}
.datagrid-body .datagrid-editable table {
  width: 100%;
  height: 100%;
}
.datagrid-body .datagrid-editable td {
  border: 0;
  margin: 0;
  padding: 0;
}
.datagrid-view .datagrid-editable-input {
  margin: 0;
  padding: 2px 4px;
  border: 1px solid #000;
  font-size: 14px;
  outline-style: none;
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.datagrid-view .validatebox-invalid {
  border-color: #ffa8a8;
}
.datagrid-sort .datagrid-sort-icon {
  display: inline;
  padding: 0 13px 0 0;
  background: url('images/datagrid_icons.png') no-repeat -64px center;
}
.datagrid-sort-desc .datagrid-sort-icon {
  display: inline;
  padding: 0 13px 0 0;
  background: url('images/datagrid_icons.png') no-repeat -16px center;
}
.datagrid-sort-asc .datagrid-sort-icon {
  display: inline;
  padding: 0 13px 0 0;
  background: url('images/datagrid_icons.png') no-repeat 0px center;
}
.datagrid-row-collapse {
  background: url('images/datagrid_icons.png') no-repeat -48px center;
}
.datagrid-row-expand {
  background: url('images/datagrid_icons.png') no-repeat -32px center;
}
.datagrid-mask-msg {
  background: #666 url('images/loading.gif') no-repeat scroll 5px center;
}
.datagrid-header,
.datagrid-td-rownumber {
  background-color: #444;
  background: -webkit-linear-gradient(top,#4c4c4c 0,#3f3f3f 100%);
  background: -moz-linear-gradient(top,#4c4c4c 0,#3f3f3f 100%);
  background: -o-linear-gradient(top,#4c4c4c 0,#3f3f3f 100%);
  background: linear-gradient(to bottom,#4c4c4c 0,#3f3f3f 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#4c4c4c,endColorstr=#3f3f3f,GradientType=0);
}
.datagrid-cell-rownumber {
  color: #fff;
}
.datagrid-resize-proxy {
  background: #cccccc;
}
.datagrid-mask {
  background: #000;
}
.datagrid-mask-msg {
  border-color: #000;
}
.datagrid-toolbar,
.datagrid-pager {
  background: #555;
}
.datagrid-header,
.datagrid-toolbar,
.datagrid-pager,
.datagrid-footer-inner {
  border-color: #222;
}
.datagrid-header td,
.datagrid-body td,
.datagrid-footer td {
  border-color: #222;
}
.datagrid-htable,
.datagrid-btable,
.datagrid-ftable {
  color: #fff;
  border-collapse: separate;
}
.datagrid-row-alt {
  background: #555;
}
.datagrid-row-over,
.datagrid-header td.datagrid-header-over {
  background: #777;
  color: #fff;
  cursor: default;
}
.datagrid-row-selected {
  background: #0052A3;
  color: #fff;
}
.datagrid-row-editing .textbox,
.datagrid-row-editing .textbox-text {
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.datagrid-header .datagrid-filter-row td.datagrid-header-over {
  background: inherit;
}
.datagrid-split-proxy {
  position: absolute;
  left: 0;
  top: 0;
  width: 1px;
  height: 100%;
  border-left: 1px solid #00458a;
}
.datagrid-moving-proxy {
  border: 1px solid #00458a;
  height: 32px;
  line-height: 32px;
  padding: 0 4px;
}
