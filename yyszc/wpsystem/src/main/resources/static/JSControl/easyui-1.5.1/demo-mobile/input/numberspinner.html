<!doctype html>
<html>
<head>
    <meta charset="UTF-8">  
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>NumberSpinner - jQuery EasyUI Mobile Demo</title>  
    <link rel="stylesheet" type="text/css" href="../../themes/metro/easyui.css">  
    <link rel="stylesheet" type="text/css" href="../../themes/mobile.css">  
    <link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
    <script type="text/javascript" src="../../jquery.min.js"></script>  
    <script type="text/javascript" src="../../jquery.easyui.min.js"></script> 
    <script type="text/javascript" src="../../jquery.easyui.mobile.js"></script> 
</head>
<body>
	<div class="easyui-navpanel">
		<header>
			<div class="m-toolbar">
				<div class="m-title">NumberSpinner</div>
			</div>
		</header>
		<ul class="m-list">
			<li>
				<input class="easyui-numberspinner" style="width:100%;" data-options="
						label: 'Basic Number:',
						labelPosition: 'top'
						">
			</li>
			<li>
				<input class="easyui-numberspinner" style="width:100%;" data-options="
						label: 'Increment Number:',
						labelPosition: 'top',
						value: 1000,
						increment: 100
						">
			</li>
			<li>
				<input class="easyui-numberspinner" style="width:100%;" data-options="
						label: 'Format Number:',
						labelPosition: 'top',
						value: 23893,
						groupSeparator: ',',
						decimalSeparator: '.',
						prefix: '$'
						">
			</li>
		</ul>
	</div>
</body>	
</html>
