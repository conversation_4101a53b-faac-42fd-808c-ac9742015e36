.switchbutton {
  text-decoration: none;
  display: inline-block;
  overflow: hidden;
  vertical-align: middle;
  margin: 0;
  padding: 0;
  cursor: pointer;
  background: #bbb;
  border: 1px solid #bbb;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.switchbutton-inner {
  display: inline-block;
  overflow: hidden;
  position: relative;
  top: -1px;
  left: -1px;
}
.switchbutton-on,
.switchbutton-off,
.switchbutton-handle {
  display: inline-block;
  text-align: center;
  height: 100%;
  float: left;
  font-size: 14px;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.switchbutton-on {
  background: #ffe48d;
  color: #000000;
}
.switchbutton-off {
  background-color: #ffffff;
  color: #000000;
}
.switchbutton-on,
.switchbutton-reversed .switchbutton-off {
  -moz-border-radius: 5px 0 0 5px;
  -webkit-border-radius: 5px 0 0 5px;
  border-radius: 5px 0 0 5px;
}
.switchbutton-off,
.switchbutton-reversed .switchbutton-on {
  -moz-border-radius: 0 5px 5px 0;
  -webkit-border-radius: 0 5px 5px 0;
  border-radius: 0 5px 5px 0;
}
.switchbutton-handle {
  position: absolute;
  top: 0;
  left: 50%;
  background-color: #ffffff;
  color: #000000;
  border: 1px solid #bbb;
  -moz-box-shadow: 0 0 3px 0 #bbb;
  -webkit-box-shadow: 0 0 3px 0 #bbb;
  box-shadow: 0 0 3px 0 #bbb;
}
.switchbutton-value {
  position: absolute;
  top: 0;
  left: -5000px;
}
.switchbutton-disabled {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.switchbutton-disabled,
.switchbutton-readonly {
  cursor: default;
}
.switchbutton:focus {
  -moz-box-shadow: 0 0 3px 0 #bbb;
  -webkit-box-shadow: 0 0 3px 0 #bbb;
  box-shadow: 0 0 3px 0 #bbb;
  outline: none;
}
