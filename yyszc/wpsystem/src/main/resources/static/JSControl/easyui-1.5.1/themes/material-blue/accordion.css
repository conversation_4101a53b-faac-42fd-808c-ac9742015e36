.accordion {
  overflow: hidden;
  border-width: 1px;
  border-style: solid;
}
.accordion .accordion-header {
  border-width: 0 0 1px;
  cursor: pointer;
}
.accordion .accordion-body {
  border-width: 0 0 1px;
}
.accordion-noborder {
  border-width: 0;
}
.accordion-noborder .accordion-header {
  border-width: 0 0 1px;
}
.accordion-noborder .accordion-body {
  border-width: 0 0 1px;
}
.accordion-collapse {
  background: url('images/accordion_arrows.png') no-repeat 0 0;
}
.accordion-expand {
  background: url('images/accordion_arrows.png') no-repeat -16px 0;
}
.accordion {
  background: #ffffff;
  border-color: #dfdfdf;
}
.accordion .accordion-header {
  background: #f5f5f5;
  filter: none;
}
.accordion .accordion-header-selected {
  background: #eee;
}
.accordion .accordion-header-selected .panel-title {
  color: #2196f3;
}
.accordion .panel-last > .accordion-header {
  border-bottom-color: #f5f5f5;
}
.accordion .panel-last > .accordion-body {
  border-bottom-color: #ffffff;
}
.accordion .panel-last > .accordion-header-selected,
.accordion .panel-last > .accordion-header-border {
  border-bottom-color: #dfdfdf;
}
.accordion> .panel-hleft {
  float: left;
}
.accordion> .panel-hleft>.panel-header {
  border-width: 0 1px 0 0;
}
.accordion> .panel-hleft> .panel-body {
  border-width: 0 1px 0 0;
}
.accordion> .panel-hleft.panel-last > .accordion-header {
  border-right-color: #f5f5f5;
}
.accordion> .panel-hleft.panel-last > .accordion-body {
  border-right-color: #ffffff;
}
.accordion> .panel-hleft.panel-last > .accordion-header-selected,
.accordion> .panel-hleft.panel-last > .accordion-header-border {
  border-right-color: #dfdfdf;
}
.accordion> .panel-hright {
  float: right;
}
.accordion> .panel-hright>.panel-header {
  border-width: 0 0 0 1px;
}
.accordion> .panel-hright> .panel-body {
  border-width: 0 0 0 1px;
}
.accordion> .panel-hright.panel-last > .accordion-header {
  border-left-color: #f5f5f5;
}
.accordion> .panel-hright.panel-last > .accordion-body {
  border-left-color: #ffffff;
}
.accordion> .panel-hright.panel-last > .accordion-header-selected,
.accordion> .panel-hright.panel-last > .accordion-header-border {
  border-left-color: #dfdfdf;
}
