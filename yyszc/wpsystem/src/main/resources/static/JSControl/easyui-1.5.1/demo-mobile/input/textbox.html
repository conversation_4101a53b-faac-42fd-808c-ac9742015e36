<!doctype html>
<html>
<head>
    <meta charset="UTF-8">  
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>TextBox - jQuery EasyUI Mobile Demo</title>  
    <link rel="stylesheet" type="text/css" href="../../themes/metro/easyui.css">  
    <link rel="stylesheet" type="text/css" href="../../themes/mobile.css">  
    <link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
    <script type="text/javascript" src="../../jquery.min.js"></script>  
    <script type="text/javascript" src="../../jquery.easyui.min.js"></script> 
    <script type="text/javascript" src="../../jquery.easyui.mobile.js"></script> 
</head>
<body>
	<div class="easyui-navpanel">
		<header>
			<div class="m-toolbar">
				<div class="m-title">TextBox</div>
			</div>
		</header>
		<ul class="m-list">
			<li>
				<input class="easyui-textbox" style="width:100%" data-options="
						label: 'Standard TextBox:',
						labelPosition: 'top',
						prompt: 'Search...'
						">
			</li>
			<li>
				<input class="easyui-textbox" style="width:100%" data-options="
						label: 'Icon:',
						labelPosition: 'top',
						iconCls: 'icon-search'
						">
			</li>
			<li>
				<input class="easyui-textbox" style="width:100%" data-options="
						label: 'Button:',
						labelPosition: 'top',
						buttonText: 'Search'
						">
			</li>
		</ul>

	</div>
</body>	
</html>
