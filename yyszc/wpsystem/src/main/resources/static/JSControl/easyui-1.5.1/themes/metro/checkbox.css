.checkbox {
  position: relative;
  border: 2px solid #99cdff;
  -moz-border-radius: 0px 0px 0px 0px;
  -webkit-border-radius: 0px 0px 0px 0px;
  border-radius: 0px 0px 0px 0px;
}
.checkbox-checked {
  border: 0;
  background: #99cdff;
}
.checkbox-inner {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.checkbox path {
  stroke-width: 2px;
}
.checkbox-disabled {
  opacity: 0.6;
}
.checkbox-value {
  position: absolute;
  overflow: hidden;
  width: 1px;
  height: 1px;
  left: -100px;
}
